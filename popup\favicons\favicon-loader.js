/**
 * Sistema de Carregamento de Favicons
 * 
 * Gerencia o carregamento assíncrono de favicons com fallbacks,
 * controle de concorrência e otimizações de performance.
 */

window.FaviconLoader = window.FaviconLoader || {};

(function() {
    'use strict';

    // Configurações do carregador
    const LOADER_CONFIG = {
        // Timeouts
        LOAD_TIMEOUT: 8000, // 8 segundos
        RETRY_TIMEOUT: 2000, // 2 segundos entre tentativas

        // Controle de concorrência
        MAX_CONCURRENT_LOADS: 6,
        QUEUE_BATCH_SIZE: 10,

        // Tentativas e fallbacks
        MAX_RETRIES: 2,
        FALLBACK_FAVICON: '../img/icons/star_blue_ext.png',
        
        // Tamanhos de favicon suportados
        FAVICON_SIZES: [16, 32, 64],
        DEFAULT_SIZE: 16,
        
        // Prefixos de URL interna
        INTERNAL_URL_PREFIXES: [
            'chrome://', 'edge://', 'about:', 'browser://', 
            'chrome-extension://', 'moz-extension://'
        ]
    };

    // Estado do carregador
    let activeLoads = 0;
    let loadQueue = [];
    let isProcessingQueue = false;
    
    // Cache de URLs de favicon geradas
    let faviconUrlCache = new Map();
    
    // Estatísticas
    let stats = {
        totalLoads: 0,
        successfulLoads: 0,
        failedLoads: 0,
        cacheHits: 0,
        fallbackUsed: 0
    };

    /**
     * Detecta o navegador atual
     * @returns {string} Nome do navegador
     */
    function detectBrowser() {
        const userAgent = navigator.userAgent.toLowerCase();
        
        if (userAgent.includes('edg/')) return 'edge';
        if (userAgent.includes('chrome')) return 'chrome';
        if (userAgent.includes('firefox')) return 'firefox';
        
        return 'chrome'; // fallback
    }

    /**
     * Verifica se uma URL é interna do navegador
     * @param {string} url - URL para verificar
     * @returns {boolean} True se for URL interna
     */
    function isInternalUrl(url) {
        if (!url) return true;
        
        return LOADER_CONFIG.INTERNAL_URL_PREFIXES.some(prefix => 
            url.toLowerCase().startsWith(prefix)
        );
    }

    /**
     * Gera a URL do favicon baseada no navegador e URL
     * @param {string} url - URL do site
     * @param {number} size - Tamanho do favicon
     * @returns {string} URL do favicon
     */
    function generateFaviconUrl(url, size = LOADER_CONFIG.DEFAULT_SIZE) {
        if (!url || isInternalUrl(url)) {
            return LOADER_CONFIG.FALLBACK_FAVICON;
        }

        // Verificar cache de URLs geradas
        const cacheKey = `${url}_${size}`;
        if (faviconUrlCache.has(cacheKey)) {
            return faviconUrlCache.get(cacheKey);
        }

        try {
            const urlObj = new URL(url);
            const browser = detectBrowser();
            let faviconUrl;

            switch (browser) {
                case 'edge':
                    faviconUrl = `edge://favicon/size/${size}@1x/${url}`;
                    break;
                case 'firefox':
                    // Firefox usa um sistema diferente
                    faviconUrl = `moz-extension://favicon/${urlObj.hostname}`;
                    break;
                default: // chrome
                    faviconUrl = `chrome://favicon/size/${size}@1x/${url}`;
                    break;
            }

            // Cache da URL gerada
            faviconUrlCache.set(cacheKey, faviconUrl);
            return faviconUrl;

        } catch (error) {
            console.warn(`[FaviconLoader] URL inválida: ${url}`, error);
            return LOADER_CONFIG.FALLBACK_FAVICON;
        }
    }

    /**
     * Carrega um favicon com timeout e retry
     * @param {string} faviconUrl - URL do favicon
     * @param {number} retryCount - Número de tentativas restantes
     * @returns {Promise<string>} URL do favicon carregado
     */
    function loadFaviconWithRetry(faviconUrl, retryCount = LOADER_CONFIG.MAX_RETRIES) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            const timeout = setTimeout(() => {
                img.onload = img.onerror = null;
                
                if (retryCount > 0) {
                    console.log(`[FaviconLoader] Timeout, tentando novamente: ${faviconUrl}`);
                    setTimeout(() => {
                        loadFaviconWithRetry(faviconUrl, retryCount - 1)
                            .then(resolve)
                            .catch(reject);
                    }, LOADER_CONFIG.RETRY_TIMEOUT);
                } else {
                    reject(new Error('Timeout após todas as tentativas'));
                }
            }, LOADER_CONFIG.LOAD_TIMEOUT);

            img.onload = () => {
                clearTimeout(timeout);
                resolve(faviconUrl);
            };

            img.onerror = () => {
                clearTimeout(timeout);
                
                if (retryCount > 0) {
                    console.log(`[FaviconLoader] Erro, tentando novamente: ${faviconUrl}`);
                    setTimeout(() => {
                        loadFaviconWithRetry(faviconUrl, retryCount - 1)
                            .then(resolve)
                            .catch(reject);
                    }, LOADER_CONFIG.RETRY_TIMEOUT);
                } else {
                    reject(new Error('Erro após todas as tentativas'));
                }
            };

            img.src = faviconUrl;
        });
    }

    /**
     * Processa a fila de carregamento
     */
    async function processQueue() {
        if (isProcessingQueue || loadQueue.length === 0) return;
        
        isProcessingQueue = true;

        while (loadQueue.length > 0 && activeLoads < LOADER_CONFIG.MAX_CONCURRENT_LOADS) {
            const batch = loadQueue.splice(0, LOADER_CONFIG.QUEUE_BATCH_SIZE);
            
            batch.forEach(async (loadRequest) => {
                activeLoads++;
                
                try {
                    await processLoadRequest(loadRequest);
                } catch (error) {
                    console.error('[FaviconLoader] Erro ao processar requisição:', error);
                } finally {
                    activeLoads--;
                    
                    // Continuar processando a fila se houver mais itens
                    if (loadQueue.length > 0) {
                        setTimeout(processQueue, 10);
                    }
                }
            });
        }

        isProcessingQueue = false;
    }

    /**
     * Processa uma requisição de carregamento individual
     * @param {Object} loadRequest - Requisição de carregamento
     */
    async function processLoadRequest(loadRequest) {
        const { imgElement, url, size, onSuccess, onError } = loadRequest;
        
        stats.totalLoads++;

        try {
            // Verificar cache primeiro
            const cachedEntry = window.FaviconCache?.get(url);
            if (cachedEntry) {
                stats.cacheHits++;
                imgElement.src = cachedEntry.faviconUrl;
                if (onSuccess) onSuccess(cachedEntry.faviconUrl);
                return;
            }

            // Gerar URL do favicon
            const faviconUrl = generateFaviconUrl(url, size);
            
            // Se for fallback, usar diretamente
            if (faviconUrl === LOADER_CONFIG.FALLBACK_FAVICON) {
                stats.fallbackUsed++;
                imgElement.src = faviconUrl;
                if (onSuccess) onSuccess(faviconUrl);
                return;
            }

            // Tentar carregar o favicon
            const loadedUrl = await loadFaviconWithRetry(faviconUrl);
            
            // Sucesso - atualizar elemento e cache
            imgElement.src = loadedUrl;
            stats.successfulLoads++;
            
            // Salvar no cache
            if (window.FaviconCache?.set) {
                window.FaviconCache.set(url, loadedUrl);
            }
            
            if (onSuccess) onSuccess(loadedUrl);

        } catch (error) {
            // Falha - usar fallback
            stats.failedLoads++;
            stats.fallbackUsed++;
            
            imgElement.src = LOADER_CONFIG.FALLBACK_FAVICON;
            
            // Ainda salvar no cache para evitar tentativas futuras
            if (window.FaviconCache?.set) {
                window.FaviconCache.set(url, LOADER_CONFIG.FALLBACK_FAVICON);
            }
            
            if (onError) onError(error);
        }
    }

    /**
     * Carrega um favicon para um elemento img
     * @param {HTMLImageElement} imgElement - Elemento img
     * @param {string} url - URL do site
     * @param {Object} options - Opções de carregamento
     */
    function loadFavicon(imgElement, url, options = {}) {
        if (!imgElement || !url) {
            console.warn('[FaviconLoader] Parâmetros inválidos');
            return;
        }

        const {
            size = LOADER_CONFIG.DEFAULT_SIZE,
            onSuccess = null,
            onError = null,
            priority = false
        } = options;

        // Definir favicon padrão imediatamente
        imgElement.src = LOADER_CONFIG.FALLBACK_FAVICON;

        // Criar requisição de carregamento
        const loadRequest = {
            imgElement,
            url,
            size,
            onSuccess,
            onError,
            timestamp: Date.now()
        };

        // Adicionar à fila (prioridade vai para o início)
        if (priority) {
            loadQueue.unshift(loadRequest);
        } else {
            loadQueue.push(loadRequest);
        }

        // Processar fila
        processQueue();
    }

    /**
     * Pré-carrega favicons para uma lista de URLs
     * @param {Array<string>} urls - Lista de URLs
     * @param {Object} options - Opções de pré-carregamento
     */
    function preloadFavicons(urls, options = {}) {
        if (!Array.isArray(urls)) return;

        const { size = LOADER_CONFIG.DEFAULT_SIZE, batchDelay = 100 } = options;

        urls.forEach((url, index) => {
            setTimeout(() => {
                const tempImg = document.createElement('img');
                tempImg.style.display = 'none';
                
                loadFavicon(tempImg, url, { 
                    size,
                    onSuccess: () => {
                        // Remover elemento temporário após carregamento
                        if (tempImg.parentNode) {
                            tempImg.parentNode.removeChild(tempImg);
                        }
                    }
                });
            }, index * batchDelay);
        });
    }

    /**
     * Obtém estatísticas do carregador
     * @returns {Object} Estatísticas
     */
    function getStats() {
        const successRate = stats.totalLoads > 0 
            ? (stats.successfulLoads / stats.totalLoads * 100).toFixed(2)
            : 0;

        return {
            ...stats,
            successRate: `${successRate}%`,
            activeLoads,
            queueSize: loadQueue.length,
            faviconUrlCacheSize: faviconUrlCache.size
        };
    }

    /**
     * Limpa caches e estatísticas
     */
    function clear() {
        loadQueue.length = 0;
        faviconUrlCache.clear();
        stats = {
            totalLoads: 0,
            successfulLoads: 0,
            failedLoads: 0,
            cacheHits: 0,
            fallbackUsed: 0
        };
        
        console.log('[FaviconLoader] Carregador limpo');
    }

    // API pública
    window.FaviconLoader = {
        loadFavicon,
        preloadFavicons,
        generateFaviconUrl,
        getStats,
        clear,
        
        // Configurações (somente leitura)
        config: Object.freeze({...LOADER_CONFIG})
    };

})();
