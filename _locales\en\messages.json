{"extName": {"message": "Bookmark Folder Merger", "description": "Extension name"}, "extDescription": {"message": "Sort bookmarks and merge folders with advanced organization features", "description": "Extension description"}, "optionsTitle": {"message": "Bookmark Folder Merger Settings", "description": "Title of the options page"}, "appearance": {"message": "Appearance", "description": "Appearance section title"}, "theme": {"message": "Theme", "description": "Theme option label"}, "themeLight": {"message": "Light", "description": "Light theme option"}, "themeDark": {"message": "Dark", "description": "Dark theme option"}, "themeSystem": {"message": "Use system theme", "description": "System theme option"}, "primaryColor": {"message": "Primary color", "description": "Primary color option label"}, "fontSize": {"message": "Font size", "description": "Font size option label"}, "fontSizeSmall": {"message": "Small", "description": "Small font size option"}, "fontSizeMedium": {"message": "Medium", "description": "Medium font size option"}, "fontSizeLarge": {"message": "Large", "description": "Large font size option"}, "behavior": {"message": "Behavior", "description": "Behavior section title"}, "startupAction": {"message": "Startup action", "description": "Startup action option label"}, "startupActionNone": {"message": "No action", "description": "No startup action option"}, "startupActionLast": {"message": "Show last selected folders", "description": "Show last selected folders option"}, "startupActionFavorites": {"message": "Show favorite folders", "description": "Show favorite folders option"}, "maxBookmarks": {"message": "Maximum bookmarks displayed", "description": "Maximum bookmarks option label"}, "maxBookmarksWarning": {"message": "A very high number may cause slowness", "description": "Warning about high number of bookmarks"}, "confirmDelete": {"message": "Confirm before deleting", "description": "Confirm delete option label"}, "confirmDeleteDesc": {"message": "Ask for confirmation before deleting bookmarks", "description": "Confirm delete option description"}, "confirmMerge": {"message": "Confirm before merging", "description": "Confirm merge option label"}, "confirmMergeDesc": {"message": "Ask for confirmation before merging folders", "description": "Confirm merge option description"}, "favoriteFolders": {"message": "Favorite Folders", "description": "Favorite folders section title"}, "quickAccessFolders": {"message": "Folders for quick access", "description": "Quick access folders label"}, "noFavoriteFolders": {"message": "No favorite folders configured. Add folders in the main interface.", "description": "No favorite folders message"}, "backupRestore": {"message": "Backup and Restore", "description": "Backup and restore section title"}, "settingsBackup": {"message": "Settings backup", "description": "Settings backup label"}, "exportSettings": {"message": "Export settings", "description": "Export settings button text"}, "exportSettingsDesc": {"message": "Save all settings to a JSON file", "description": "Export settings description"}, "restoreSettings": {"message": "Restore settings", "description": "Restore settings label"}, "importSettings": {"message": "Import settings", "description": "Import settings button text"}, "resetDefaults": {"message": "Reset to defaults", "description": "Reset to defaults button text"}, "saveSettings": {"message": "Save settings", "description": "Save settings button text"}, "settingsSaved": {"message": "Setting<PERSON> saved successfully!", "description": "Settings saved notification"}, "settingsError": {"message": "Error saving settings", "description": "Settings error notification"}, "settingsReset": {"message": "Settings reset to default values", "description": "Settings reset notification"}, "settingsExported": {"message": "Settings exported successfully!", "description": "Settings exported notification"}, "settingsImported": {"message": "Settings imported successfully!", "description": "Settings imported notification"}, "invalidSettingsFile": {"message": "Invalid settings file", "description": "Invalid settings file notification"}, "resetConfirm": {"message": "Are you sure you want to reset all settings to default values?", "description": "Reset confirmation message"}, "select": {"message": "Select", "description": "Select button text"}, "deselect": {"message": "Deselect", "description": "Deselect button text"}, "merge": {"message": "<PERSON><PERSON>", "description": "Merge button text"}, "sort": {"message": "Sort", "description": "Sort button text"}, "delete": {"message": "Delete", "description": "Delete button text"}, "copy": {"message": "Copy", "description": "Copy button text"}, "sortByTitle": {"message": "Sort by title", "description": "Sort by title option"}, "sortByUrl": {"message": "Sort by URL", "description": "Sort by URL option"}, "sortByDomain": {"message": "Sort by domain", "description": "Sort by domain option"}, "sortByDateAdded": {"message": "Sort by date added", "description": "Sort by date added option"}, "sortByDateModified": {"message": "Sort by date modified", "description": "Sort by date modified option"}, "copyTitles": {"message": "Copy titles", "description": "Copy titles option"}, "copyUrls": {"message": "Copy URLs", "description": "Copy URLs option"}, "copyBoth": {"message": "Copy titles and URLs", "description": "Copy both option"}, "folderSearch": {"message": "Search folders...", "description": "Folder search placeholder"}, "bookmarkSearch": {"message": "Search bookmarks...", "description": "Bookmark search placeholder"}, "foldersDisplayed": {"message": "Folders displayed: $COUNT$", "description": "Folders displayed count", "placeholders": {"count": {"content": "$1", "example": "42"}}}, "bookmarksDisplayed": {"message": "Bookmarks displayed: $COUNT$", "description": "Bookmarks displayed count", "placeholders": {"count": {"content": "$1", "example": "42"}}}, "bookmarksSelected": {"message": "Selected: $COUNT$", "description": "Bookmarks selected count", "placeholders": {"count": {"content": "$1", "example": "42"}}}, "noFoldersSelected": {"message": "No folders selected", "description": "No folders selected message"}, "noBookmarksFound": {"message": "No bookmarks found", "description": "No bookmarks found message"}, "deleteConfirm": {"message": "Are you sure you want to delete $COUNT$ bookmarks?", "description": "Delete confirmation message", "placeholders": {"count": {"content": "$1", "example": "42"}}}, "mergeConfirm": {"message": "Are you sure you want to merge $COUNT$ folders?", "description": "Merge confirmation message", "placeholders": {"count": {"content": "$1", "example": "42"}}}, "bookmarkDeleted": {"message": "Bookmark deleted successfully", "description": "Bookmark deleted notification"}, "bookmarksDeleted": {"message": "$COUNT$ bookmarks deleted successfully", "description": "Bookmarks deleted notification", "placeholders": {"count": {"content": "$1", "example": "42"}}}, "foldersMerged": {"message": "Folders merged successfully", "description": "Folders merged notification"}, "bookmarksSorted": {"message": "Bookmarks sorted successfully", "description": "Bookmarks sorted notification"}, "copied": {"message": "Copied to clipboard", "description": "Copied to clipboard notification"}, "error": {"message": "An error occurred", "description": "Generic error message"}}