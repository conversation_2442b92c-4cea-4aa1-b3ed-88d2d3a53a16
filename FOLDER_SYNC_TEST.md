# Teste de Sincronização de Pastas - Guia de Debug

## Problema Reportado

A lista de pastas não está atualizando quando pastas são excluídas externamente.

## ✅ Soluções Implementadas

### **1. Sistema de Listeners (background.js)**
- Detecta mudanças via `chrome.bookmarks.onRemoved`
- Envia notificações para abas abertas

### **2. Sistema de Sincronização (bookmark-sync.js)**
- Recebe notificações do background
- Atualiza interface automaticamente

### **3. Monitor Direto (folder-monitor.js) - NOVO**
- Verifica mudanças a cada 2 segundos
- Não depende de mensagens entre scripts
- Solução mais robusta e confiável

## 🧪 Como Testar

### **Passo 1: Recarregar Extensão**
1. Vá para `chrome://extensions/`
2. Encontre a extensão "Bookmark Manager & Folder Merger"
3. Clique em "Recarregar" (ícone de refresh)

### **Passo 2: Abrir DevTools**
1. Abra a extensão (clique no ícone)
2. Pressione **F12** para abrir DevTools
3. Vá para a aba **Console**

### **Passo 3: Verificar Sistemas**
Execute estes comandos no console:

```javascript
// Verificar se o monitor está ativo
folderMonitorStats()

// Verificar sistema de sincronização
window.BookmarkSync.getStats()

// Forçar sincronização
forceBookmarkSync()
```

### **Passo 4: Teste de Exclusão**
1. **Selecione algumas pastas** na extensão
2. **Abra uma nova aba** do navegador
3. **Vá para** `chrome://bookmarks/` (ou `edge://bookmarks/`)
4. **Exclua uma pasta** que estava selecionada na extensão
5. **Volte para a extensão** (aguarde até 2 segundos)
6. **Verifique** se a pasta foi removida automaticamente

## 📊 Logs Esperados

### **No Console da Extensão:**
```
[FolderMonitor] Sistema carregado
[FolderMonitor] Iniciando monitoramento...
[FolderMonitor] Snapshot criado: X pastas, Y bookmarks
[FolderMonitor] Monitoramento ativo
```

### **Quando Pasta é Excluída:**
```
[FolderMonitor] Pasta removida: 123 (Nome da Pasta)
[FolderMonitor] Elemento da pasta 123 removido da interface
```

### **No Console do Background (chrome://extensions/ → Detalhes → Inspecionar visualizações → background.html):**
```
[Background] Bookmark/Pasta removido: 123 {parentId: "1", index: 0, node: {...}}
[Background] Notificando mudança: removed {...}
[Background] Enviando para X abas
```

## 🔧 Comandos de Debug

### **Console da Extensão:**
```javascript
// Ver estatísticas do monitor
folderMonitorStats()

// Parar monitoramento
stopFolderMonitor()

// Iniciar monitoramento
startFolderMonitor()

// Forçar sincronização completa
forceBookmarkSync()

// Ver estatísticas de sincronização
window.BookmarkSync.getStats()
```

### **Console do Background:**
```javascript
// Testar notificação manual
notifyBookmarkChange('test', {id: '123', message: 'teste'})
```

## 🚨 Solução de Problemas

### **Problema: Nenhum log aparece**
**Solução:**
1. Recarregue a extensão completamente
2. Feche e reabra o popup
3. Verifique se há erros no console

### **Problema: Monitor não inicia**
**Solução:**
```javascript
// Forçar início do monitor
startFolderMonitor()

// Verificar status
folderMonitorStats()
```

### **Problema: Listeners não funcionam**
**Solução:**
1. Abra `chrome://extensions/`
2. Clique em "Detalhes" da extensão
3. Clique em "Inspecionar visualizações" → "background.html"
4. Verifique logs no console do background

### **Problema: Interface não atualiza**
**Solução:**
```javascript
// Forçar atualização completa
forceBookmarkSync()

// Recriar snapshot
window.FolderMonitor.createSnapshot()
```

## 📋 Checklist de Teste

### **✅ Teste Básico**
- [ ] Extensão carrega sem erros
- [ ] Monitor inicia automaticamente
- [ ] Logs aparecem no console
- [ ] Estatísticas mostram dados corretos

### **✅ Teste de Exclusão**
- [ ] Pasta é removida da interface
- [ ] Favoritos da pasta são removidos
- [ ] Contadores são atualizados
- [ ] Mensagem de feedback aparece

### **✅ Teste de Criação**
- [ ] Nova pasta aparece na lista
- [ ] Mensagem de feedback aparece

### **✅ Teste de Renomeação**
- [ ] Nome da pasta é atualizado
- [ ] Mensagem de feedback aparece

## 🔄 Sistemas Redundantes

A extensão agora tem **3 sistemas** para detectar mudanças:

1. **Background Listeners** - Detecção em tempo real
2. **Message Sync** - Comunicação entre scripts
3. **Direct Monitor** - Verificação periódica (mais confiável)

Se um sistema falhar, os outros continuam funcionando!

## 📞 Próximos Passos

1. **Teste** seguindo este guia
2. **Reporte** quais logs aparecem no console
3. **Informe** se a pasta é removida automaticamente
4. **Compartilhe** qualquer erro que aparecer

O sistema **Direct Monitor** deve funcionar mesmo se os outros falharem, pois verifica diretamente a API do Chrome a cada 2 segundos.
