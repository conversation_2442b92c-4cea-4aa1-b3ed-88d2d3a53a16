/**
 * Sistema de Menu de Contexto para Favoritos e Pastas
 * Substitui o sistema de clique direito anterior
 */

let contextMenu = null;
let contextTarget = null;
let contextTargetType = null; // 'folder', 'bookmark', 'multiple'
let contextSelectedItems = null; // Armazena os itens selecionados da coluna específica

/**
 * Inicializa o sistema de menu de contexto
 */
function initContextMenu() {
  contextMenu = document.getElementById('contextMenu');
  
  // Configurar eventos de clique direito
  setupContextMenuEvents();
  
  // Configurar eventos de clique nos itens do menu
  setupContextMenuActions();
  
  // Fechar menu ao clicar fora
  document.addEventListener('click', hideContextMenu);
  document.addEventListener('contextmenu', (e) => {
    // Permitir menu de contexto apenas em elementos específicos
    const isValidTarget = e.target.closest('.folder-option') || e.target.closest('.bookmark-item');
    if (!isValidTarget) {
      hideContextMenu();
    }
  });
}

/**
 * Configura os eventos de clique direito
 */
function setupContextMenuEvents() {
  // Menu de contexto para pastas
  const foldersContainer = document.getElementById('folderCheckboxes');
  if (foldersContainer) {
    foldersContainer.addEventListener('contextmenu', handleFolderContextMenu);
  }
  
  // Menu de contexto para favoritos
  const bookmarksContainer = document.getElementById('bookmarksContainer');
  if (bookmarksContainer) {
    bookmarksContainer.addEventListener('contextmenu', handleBookmarkContextMenu);
  }
}

/**
 * Manipula clique direito em pastas
 */
function handleFolderContextMenu(e) {
  e.preventDefault();

  const folderOption = e.target.closest('.folder-option');
  if (!folderOption) return;

  contextTarget = folderOption;
  const checkbox = folderOption.querySelector('.folder-checkbox');
  const folderId = checkbox ? checkbox.value : null;

  if (!folderId) return;

  // Verificar se há múltiplas PASTAS selecionadas (apenas da coluna esquerda)
  const selectedFolders = getSelectedFolderElements();

  if (selectedFolders.length > 1) {
    // Armazenar contexto da seleção múltipla de pastas
    contextSelectedItems = { folders: selectedFolders, bookmarks: [] };
    // Menu para múltiplas pastas (apenas da coluna esquerda)
    showMultipleContextMenu(e.clientX, e.clientY, selectedFolders, []);
  } else {
    // Armazenar contexto da pasta individual
    contextSelectedItems = { folders: [folderOption], bookmarks: [] };
    // Menu para pasta individual
    showFolderContextMenu(e.clientX, e.clientY, folderId);
  }
}

/**
 * Manipula clique direito em favoritos
 */
function handleBookmarkContextMenu(e) {
  e.preventDefault();

  const bookmarkItem = e.target.closest('.bookmark-item');
  if (!bookmarkItem) return;

  contextTarget = bookmarkItem;
  const bookmarkId = bookmarkItem.dataset.id;

  if (!bookmarkId) return;

  // Verificar se há múltiplos FAVORITOS selecionados (apenas da coluna direita)
  const selectedBookmarks = getSelectedBookmarkElements();

  if (selectedBookmarks.length > 1) {
    // Armazenar contexto da seleção múltipla de favoritos
    contextSelectedItems = { folders: [], bookmarks: selectedBookmarks };
    // Menu para múltiplos favoritos (apenas da coluna direita)
    showMultipleContextMenu(e.clientX, e.clientY, [], selectedBookmarks);
  } else {
    // Armazenar contexto do favorito individual
    contextSelectedItems = { folders: [], bookmarks: [bookmarkItem] };
    // Menu para favorito individual
    showBookmarkContextMenu(e.clientX, e.clientY, bookmarkId);
  }
}

/**
 * Mostra menu de contexto para pasta
 */
function showFolderContextMenu(x, y, folderId) {
  contextTargetType = 'folder';
  
  // Ocultar outras opções
  document.getElementById('bookmarkContextOptions').style.display = 'none';
  document.getElementById('multipleContextOptions').style.display = 'none';
  
  // Mostrar opções de pasta
  const folderOptions = document.getElementById('folderContextOptions');
  folderOptions.style.display = 'block';
  
  // Atualizar textos com contagem de favoritos
  updateFolderContextTexts(folderId);
  
  // Posicionar e mostrar menu
  showContextMenuAt(x, y);
}

/**
 * Mostra menu de contexto para favorito
 */
function showBookmarkContextMenu(x, y, bookmarkId) {
  contextTargetType = 'bookmark';
  
  // Ocultar outras opções
  document.getElementById('folderContextOptions').style.display = 'none';
  document.getElementById('multipleContextOptions').style.display = 'none';
  
  // Mostrar opções de favorito
  const bookmarkOptions = document.getElementById('bookmarkContextOptions');
  bookmarkOptions.style.display = 'block';
  
  // Posicionar e mostrar menu
  showContextMenuAt(x, y);
}

/**
 * Mostra menu de contexto para seleção múltipla
 */
function showMultipleContextMenu(x, y, selectedFolders, selectedBookmarks) {
  contextTargetType = 'multiple';
  
  // Ocultar outras opções
  document.getElementById('folderContextOptions').style.display = 'none';
  document.getElementById('bookmarkContextOptions').style.display = 'none';
  
  // Mostrar opções múltiplas
  const multipleOptions = document.getElementById('multipleContextOptions');
  multipleOptions.style.display = 'block';
  
  // Atualizar textos com contagens
  updateMultipleContextTexts(selectedFolders.length, selectedBookmarks.length);
  
  // Posicionar e mostrar menu
  showContextMenuAt(x, y);
}

/**
 * Posiciona e mostra o menu de contexto
 */
function showContextMenuAt(x, y) {
  contextMenu.style.display = 'block';
  
  // Ajustar posição para não sair da tela
  const menuRect = contextMenu.getBoundingClientRect();
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  
  let finalX = x;
  let finalY = y;
  
  // Ajustar X se sair da tela
  if (x + menuRect.width > windowWidth) {
    finalX = windowWidth - menuRect.width - 10;
  }
  
  // Ajustar Y se sair da tela
  if (y + menuRect.height > windowHeight) {
    finalY = windowHeight - menuRect.height - 10;
  }
  
  contextMenu.style.left = `${finalX}px`;
  contextMenu.style.top = `${finalY}px`;
}

/**
 * Oculta o menu de contexto
 */
function hideContextMenu() {
  if (contextMenu) {
    contextMenu.style.display = 'none';
    contextTarget = null;
    contextTargetType = null;
    contextSelectedItems = null;
  }
}

/**
 * Atualiza textos do menu de contexto para pastas
 */
function updateFolderContextTexts(folderId) {
  // Obter contagem de favoritos na pasta
  chrome.bookmarks.getChildren(folderId, (children) => {
    if (chrome.runtime.lastError) return;
    
    const bookmarkCount = children.filter(child => child.url).length;
    
    document.getElementById('openAllText').textContent = `Abrir todos os ${bookmarkCount} favoritos`;
    document.getElementById('openAllNewWindowText').textContent = `Abrir tudo (${bookmarkCount} favoritos) em nova janela`;
    document.getElementById('openAllIncognitoText').textContent = `Abrir todos os ${bookmarkCount} favoritos em nova janela InPrivate`;
    document.getElementById('openAllTabGroupText').textContent = `Abrir todos ${bookmarkCount} favoritos em novo grupo de guias`;
  });
}

/**
 * Atualiza textos do menu de contexto para seleção múltipla
 */
function updateMultipleContextTexts(folderCount, bookmarkCount) {
  const totalCount = folderCount + bookmarkCount;

  // Determinar tipo de itens para textos mais específicos
  let itemType = '';
  if (folderCount > 0 && bookmarkCount === 0) {
    itemType = folderCount === 1 ? 'pasta' : 'pastas';
  } else if (bookmarkCount > 0 && folderCount === 0) {
    itemType = bookmarkCount === 1 ? 'favorito' : 'favoritos';
  } else {
    itemType = 'itens'; // Caso misto (não deveria acontecer com a nova lógica)
  }

  document.getElementById('deleteMultipleText').textContent = `Excluir ${totalCount} ${itemType}`;
  document.getElementById('openMultipleText').textContent = `Abrir todos os ${totalCount} ${itemType}`;
  document.getElementById('openMultipleNewWindowText').textContent = `Abrir tudo (${totalCount} ${itemType}) em nova janela`;
  document.getElementById('openMultipleIncognitoText').textContent = `Abrir todos os ${totalCount} ${itemType} em nova janela InPrivate`;
  document.getElementById('openMultipleTabGroupText').textContent = `Abrir todos ${totalCount} ${itemType} em novo grupo de guias`;
}

/**
 * Configura as ações dos itens do menu
 */
function setupContextMenuActions() {
  // Delegar eventos para todos os itens do menu
  contextMenu.addEventListener('click', (e) => {
    const contextItem = e.target.closest('.context-item');
    if (!contextItem) return;
    
    const action = contextItem.dataset.action;
    if (action) {
      executeContextAction(action);
      hideContextMenu();
    }
  });
}

/**
 * Executa a ação selecionada no menu de contexto
 */
function executeContextAction(action) {
  console.log(`Executando ação: ${action} para tipo: ${contextTargetType}`);
  
  switch (action) {
    // Ações de pasta
    case 'sort-folder':
      handleSortFolder();
      break;
    case 'rename-folder':
      handleRenameFolder();
      break;
    case 'create-subfolder':
      handleCreateSubfolder();
      break;
    case 'move-folder':
      handleMoveFolder();
      break;
    case 'move-folder-to-top':
      handleMoveFolderToTop();
      break;
    case 'delete-folder':
      handleDeleteFolder();
      break;
    case 'open-all':
      handleOpenAll();
      break;
    case 'open-all-new-window':
      handleOpenAllNewWindow();
      break;
    case 'open-all-incognito':
      handleOpenAllIncognito();
      break;
    case 'open-all-tab-group':
      handleOpenAllTabGroup();
      break;
    
    // Ações de favorito
    case 'edit-bookmark':
      handleEditBookmark();
      break;
    case 'copy-link':
      handleCopyLink();
      break;
    case 'move-bookmark':
      handleMoveBookmark();
      break;
    case 'move-bookmark-to-top':
      handleMoveBookmarkToTop();
      break;
    case 'delete-bookmark':
      handleDeleteBookmark();
      break;
    case 'open-new-tab':
      handleOpenNewTab();
      break;
    case 'open-new-window':
      handleOpenNewWindow();
      break;
    case 'open-incognito':
      handleOpenIncognito();
      break;
    
    // Ações múltiplas
    case 'delete-multiple':
      handleDeleteMultiple();
      break;
    case 'open-multiple':
      handleOpenMultiple();
      break;
    case 'open-multiple-new-window':
      handleOpenMultipleNewWindow();
      break;
    case 'open-multiple-incognito':
      handleOpenMultipleIncognito();
      break;
    case 'open-multiple-tab-group':
      handleOpenMultipleTabGroup();
      break;
    
    default:
      console.warn(`Ação não implementada: ${action}`);
      showActionFeedback(`Ação "${action}" ainda não implementada`, 'info');
  }
}

/**
 * Funções auxiliares para obter elementos selecionados
 */
function getSelectedFolderElements() {
  return Array.from(document.querySelectorAll('.folder-option .folder-checkbox:checked'))
    .map(checkbox => checkbox.closest('.folder-option'));
}

function getSelectedBookmarkElements() {
  return Array.from(document.querySelectorAll('.bookmark-item .bookmark-checkbox:checked'))
    .map(checkbox => checkbox.closest('.bookmark-item'));
}

// ===== IMPLEMENTAÇÃO DAS AÇÕES =====

/**
 * Ações para Pastas
 */
function handleSortFolder() {
  if (!contextTarget) return;

  const checkbox = contextTarget.querySelector('.folder-checkbox');
  const folderId = checkbox ? checkbox.value : null;

  if (folderId) {
    // Usar função existente de ordenação
    if (typeof handleSortBookmarksWithType === 'function') {
      handleSortBookmarksWithType('title');
      showActionFeedback('Pasta classificada por nome', 'success');
    }
  }
}

function handleRenameFolder() {
  if (!contextTarget) return;

  const checkbox = contextTarget.querySelector('.folder-checkbox');
  const folderId = checkbox ? checkbox.value : null;

  if (!folderId) return;

  chrome.bookmarks.get(folderId, (bookmarks) => {
    if (!bookmarks || !bookmarks[0]) return;

    const currentName = bookmarks[0].title;
    const newName = prompt('Digite o novo nome da pasta:', currentName);

    if (newName && newName !== currentName) {
      chrome.bookmarks.update(folderId, { title: newName }, () => {
        if (chrome.runtime.lastError) {
          showActionFeedback('Erro ao renomear pasta', 'error');
        } else {
          showActionFeedback('Pasta renomeada com sucesso', 'success');
          // Recarregar lista de pastas
          if (typeof loadBookmarkFolders === 'function') {
            loadBookmarkFolders();
          } else {
            // Fallback: recarregar página
            chrome.bookmarks.getTree((tree) => {
              const roots = tree[0].children;
              const container = document.getElementById("folderCheckboxes");
              if (typeof populateFolderCheckboxes === 'function') {
                populateFolderCheckboxes(roots, container);
              }
            });
          }
        }
      });
    }
  });
}

function handleCreateSubfolder() {
  if (!contextTarget) return;

  const checkbox = contextTarget.querySelector('.folder-checkbox');
  const parentFolderId = checkbox ? checkbox.value : null;

  if (!parentFolderId) return;

  const folderName = prompt('Digite o nome da nova subpasta:');

  if (folderName) {
    chrome.bookmarks.create({
      parentId: parentFolderId,
      title: folderName
    }, (newFolder) => {
      if (chrome.runtime.lastError) {
        showActionFeedback('Erro ao criar subpasta', 'error');
      } else {
        showActionFeedback('Subpasta criada com sucesso', 'success');
        // Recarregar lista de pastas
        if (typeof loadBookmarkFolders === 'function') {
          loadBookmarkFolders();
        } else {
          // Fallback: recarregar página
          chrome.bookmarks.getTree((tree) => {
            const roots = tree[0].children;
            const container = document.getElementById("folderCheckboxes");
            if (typeof populateFolderCheckboxes === 'function') {
              populateFolderCheckboxes(roots, container);
            }
          });
        }
      }
    });
  }
}

function handleMoveFolder() {
  if (!contextTarget) return;

  const checkbox = contextTarget.querySelector('.folder-checkbox');
  const folderId = checkbox ? checkbox.value : null;

  if (!folderId) return;

  // Criar diálogo de seleção de pasta destino
  chrome.bookmarks.getTree((tree) => {
    const dialog = document.createElement('div');
    dialog.className = 'edit-dialog';
    dialog.style.display = 'flex';

    dialog.innerHTML = `
      <div class="edit-dialog-content">
        <h3>Mover Pasta</h3>
        <div class="edit-field">
          <label for="moveFolderSelect">Selecione a pasta de destino:</label>
          <select id="moveFolderSelect" class="edit-input">
            <option value="">Selecione uma pasta...</option>
          </select>
        </div>
        <div class="edit-buttons">
          <button id="moveFolderBtn" class="primary-btn">Mover</button>
          <button id="cancelMoveBtn" class="secondary-btn">Cancelar</button>
        </div>
      </div>
    `;

    document.body.appendChild(dialog);

    const select = document.getElementById('moveFolderSelect');

    // Função para adicionar opções de pasta recursivamente
    function addFolderOptions(folders, level = 0) {
      folders.forEach(folder => {
        if (!folder.url && folder.id !== folderId) { // Não incluir a própria pasta
          const option = document.createElement('option');
          option.value = folder.id;
          option.textContent = '—'.repeat(level) + ' ' + (folder.title || '(Sem nome)');
          select.appendChild(option);

          if (folder.children) {
            addFolderOptions(folder.children, level + 1);
          }
        }
      });
    }

    addFolderOptions(tree[0].children);

    // Configurar eventos
    document.getElementById('moveFolderBtn').onclick = () => {
      const targetFolderId = select.value;
      if (!targetFolderId) {
        showActionFeedback('Selecione uma pasta de destino', 'error');
        return;
      }

      chrome.bookmarks.move(folderId, { parentId: targetFolderId }, () => {
        if (chrome.runtime.lastError) {
          showActionFeedback('Erro ao mover pasta', 'error');
        } else {
          showActionFeedback('Pasta movida com sucesso', 'success');
          if (typeof loadBookmarkFolders === 'function') {
            loadBookmarkFolders();
          }
        }
        document.body.removeChild(dialog);
      });
    };

    document.getElementById('cancelMoveBtn').onclick = () => {
      document.body.removeChild(dialog);
    };
  });
}

function handleDeleteFolder() {
  if (!contextTarget) return;

  const checkbox = contextTarget.querySelector('.folder-checkbox');
  const folderId = checkbox ? checkbox.value : null;

  if (folderId && confirm('Tem certeza que deseja excluir esta pasta?')) {
    chrome.bookmarks.removeTree(folderId, () => {
      if (chrome.runtime.lastError) {
        showActionFeedback('Erro ao excluir pasta', 'error');
      } else {
        showActionFeedback('Pasta excluída com sucesso', 'success');
        // Recarregar lista de pastas
        if (typeof loadBookmarkFolders === 'function') {
          loadBookmarkFolders();
        }
      }
    });
  }
}

function handleOpenAll() {
  if (!contextTarget) return;

  const checkbox = contextTarget.querySelector('.folder-checkbox');
  const folderId = checkbox ? checkbox.value : null;

  if (folderId) {
    openAllBookmarksInFolder(folderId, false, false);
  }
}

function handleOpenAllNewWindow() {
  if (!contextTarget) return;

  const checkbox = contextTarget.querySelector('.folder-checkbox');
  const folderId = checkbox ? checkbox.value : null;

  if (folderId) {
    openAllBookmarksInFolder(folderId, true, false);
  }
}

function handleOpenAllIncognito() {
  if (!contextTarget) return;

  const checkbox = contextTarget.querySelector('.folder-checkbox');
  const folderId = checkbox ? checkbox.value : null;

  if (folderId) {
    openAllBookmarksInFolder(folderId, true, true);
  }
}

function handleOpenAllTabGroup() {
  showActionFeedback('Função de abrir em grupo de guias em desenvolvimento', 'info');
}

/**
 * Ações para Favoritos
 */
function handleEditBookmark() {
  if (!contextTarget) return;

  const bookmarkId = contextTarget.dataset.id;
  if (bookmarkId) {
    showEditBookmarkDialog(bookmarkId);
  }
}

function handleCopyLink() {
  if (!contextTarget) return;

  const bookmarkId = contextTarget.dataset.id;
  chrome.bookmarks.get(bookmarkId, (bookmarks) => {
    if (bookmarks && bookmarks[0]) {
      navigator.clipboard.writeText(bookmarks[0].url).then(() => {
        showActionFeedback('Link copiado para a área de transferência', 'success');
      });
    }
  });
}

function handleMoveBookmark() {
  if (!contextTarget) return;

  const bookmarkId = contextTarget.dataset.id;
  if (!bookmarkId) return;

  // Criar diálogo de seleção de pasta destino
  chrome.bookmarks.getTree((tree) => {
    const dialog = document.createElement('div');
    dialog.className = 'edit-dialog';
    dialog.style.display = 'flex';

    dialog.innerHTML = `
      <div class="edit-dialog-content">
        <h3>Mover Favorito</h3>
        <div class="edit-field">
          <label for="moveBookmarkSelect">Selecione a pasta de destino:</label>
          <select id="moveBookmarkSelect" class="edit-input">
            <option value="">Selecione uma pasta...</option>
          </select>
        </div>
        <div class="edit-buttons">
          <button id="moveBookmarkBtn" class="primary-btn">Mover</button>
          <button id="cancelMoveBookmarkBtn" class="secondary-btn">Cancelar</button>
        </div>
      </div>
    `;

    document.body.appendChild(dialog);

    const select = document.getElementById('moveBookmarkSelect');

    // Função para adicionar opções de pasta recursivamente
    function addFolderOptions(folders, level = 0) {
      folders.forEach(folder => {
        if (!folder.url) { // Apenas pastas
          const option = document.createElement('option');
          option.value = folder.id;
          option.textContent = '—'.repeat(level) + ' ' + (folder.title || '(Sem nome)');
          select.appendChild(option);

          if (folder.children) {
            addFolderOptions(folder.children, level + 1);
          }
        }
      });
    }

    addFolderOptions(tree[0].children);

    // Configurar eventos
    document.getElementById('moveBookmarkBtn').onclick = () => {
      const targetFolderId = select.value;
      if (!targetFolderId) {
        showActionFeedback('Selecione uma pasta de destino', 'error');
        return;
      }

      chrome.bookmarks.move(bookmarkId, { parentId: targetFolderId }, () => {
        if (chrome.runtime.lastError) {
          showActionFeedback('Erro ao mover favorito', 'error');
        } else {
          showActionFeedback('Favorito movido com sucesso', 'success');
          if (typeof reloadSelectedFolders === 'function') {
            reloadSelectedFolders();
          }
        }
        document.body.removeChild(dialog);
      });
    };

    document.getElementById('cancelMoveBookmarkBtn').onclick = () => {
      document.body.removeChild(dialog);
    };
  });
}

function handleDeleteBookmark() {
  if (!contextTarget) return;

  const bookmarkId = contextTarget.dataset.id;
  if (bookmarkId && confirm('Tem certeza que deseja excluir este favorito?')) {
    chrome.bookmarks.remove(bookmarkId, () => {
      if (chrome.runtime.lastError) {
        showActionFeedback('Erro ao excluir favorito', 'error');
      } else {
        showActionFeedback('Favorito excluído com sucesso', 'success');
        // Recarregar favoritos
        if (typeof reloadSelectedFolders === 'function') {
          reloadSelectedFolders();
        }
      }
    });
  }
}

function handleOpenNewTab() {
  if (!contextTarget) return;

  const bookmarkId = contextTarget.dataset.id;
  chrome.bookmarks.get(bookmarkId, (bookmarks) => {
    if (bookmarks && bookmarks[0]) {
      chrome.tabs.create({ url: bookmarks[0].url });
    }
  });
}

function handleOpenNewWindow() {
  if (!contextTarget) return;

  const bookmarkId = contextTarget.dataset.id;
  chrome.bookmarks.get(bookmarkId, (bookmarks) => {
    if (bookmarks && bookmarks[0]) {
      chrome.windows.create({ url: bookmarks[0].url });
    }
  });
}

function handleOpenIncognito() {
  if (!contextTarget) return;

  const bookmarkId = contextTarget.dataset.id;
  chrome.bookmarks.get(bookmarkId, (bookmarks) => {
    if (bookmarks && bookmarks[0]) {
      chrome.windows.create({ url: bookmarks[0].url, incognito: true });
    }
  });
}

/**
 * Ações para Seleção Múltipla
 */
function handleDeleteMultiple() {
  if (!contextSelectedItems) return;

  const selectedFolders = contextSelectedItems.folders;
  const selectedBookmarks = contextSelectedItems.bookmarks;
  const totalCount = selectedFolders.length + selectedBookmarks.length;

  if (totalCount === 0) return;

  if (confirm(`Tem certeza que deseja excluir ${totalCount} itens?`)) {
    let deletedCount = 0;

    // Excluir favoritos
    selectedBookmarks.forEach(bookmark => {
      const bookmarkId = bookmark.dataset.id;
      chrome.bookmarks.remove(bookmarkId, () => {
        deletedCount++;
        if (deletedCount === totalCount) {
          showActionFeedback(`${totalCount} itens excluídos com sucesso`, 'success');
          if (typeof reloadSelectedFolders === 'function') {
            reloadSelectedFolders();
          }
        }
      });
    });

    // Excluir pastas
    selectedFolders.forEach(folder => {
      const checkbox = folder.querySelector('.folder-checkbox');
      const folderId = checkbox ? checkbox.value : null;
      if (folderId) {
        chrome.bookmarks.removeTree(folderId, () => {
          deletedCount++;
          if (deletedCount === totalCount) {
            showActionFeedback(`${totalCount} itens excluídos com sucesso`, 'success');
            if (typeof loadBookmarkFolders === 'function') {
              loadBookmarkFolders();
            }
          }
        });
      }
    });
  }
}

function handleOpenMultiple() {
  if (!contextSelectedItems) return;

  const selectedFolders = contextSelectedItems.folders;
  const selectedBookmarks = contextSelectedItems.bookmarks;

  // Abrir favoritos individuais
  selectedBookmarks.forEach(bookmark => {
    const bookmarkId = bookmark.dataset.id;
    chrome.bookmarks.get(bookmarkId, (bookmarks) => {
      if (bookmarks && bookmarks[0]) {
        chrome.tabs.create({ url: bookmarks[0].url });
      }
    });
  });

  // Abrir todos os favoritos das pastas
  selectedFolders.forEach(folder => {
    const checkbox = folder.querySelector('.folder-checkbox');
    const folderId = checkbox ? checkbox.value : null;
    if (folderId) {
      openAllBookmarksInFolder(folderId, false, false);
    }
  });
}

function handleOpenMultipleNewWindow() {
  if (!contextSelectedItems) return;

  const selectedFolders = contextSelectedItems.folders;
  const selectedBookmarks = contextSelectedItems.bookmarks;

  const allUrls = [];
  let processedCount = 0;
  const totalItems = selectedFolders.length + selectedBookmarks.length;

  if (totalItems === 0) return;

  // Processar favoritos individuais
  selectedBookmarks.forEach(bookmark => {
    const bookmarkId = bookmark.dataset.id;
    chrome.bookmarks.get(bookmarkId, (bookmarks) => {
      if (bookmarks && bookmarks[0]) {
        allUrls.push(bookmarks[0].url);
      }
      processedCount++;
      if (processedCount === totalItems) {
        openUrlsInNewWindow(allUrls);
      }
    });
  });

  // Processar pastas
  selectedFolders.forEach(folder => {
    const checkbox = folder.querySelector('.folder-checkbox');
    const folderId = checkbox ? checkbox.value : null;
    if (folderId) {
      chrome.bookmarks.getChildren(folderId, (children) => {
        const folderBookmarks = children.filter(child => child.url);
        folderBookmarks.forEach(bookmark => allUrls.push(bookmark.url));
        processedCount++;
        if (processedCount === totalItems) {
          openUrlsInNewWindow(allUrls);
        }
      });
    } else {
      processedCount++;
      if (processedCount === totalItems) {
        openUrlsInNewWindow(allUrls);
      }
    }
  });
}

function handleOpenMultipleIncognito() {
  if (!contextSelectedItems) return;

  const selectedFolders = contextSelectedItems.folders;
  const selectedBookmarks = contextSelectedItems.bookmarks;

  const allUrls = [];
  let processedCount = 0;
  const totalItems = selectedFolders.length + selectedBookmarks.length;

  if (totalItems === 0) return;

  // Processar favoritos individuais
  selectedBookmarks.forEach(bookmark => {
    const bookmarkId = bookmark.dataset.id;
    chrome.bookmarks.get(bookmarkId, (bookmarks) => {
      if (bookmarks && bookmarks[0]) {
        allUrls.push(bookmarks[0].url);
      }
      processedCount++;
      if (processedCount === totalItems) {
        openUrlsInIncognito(allUrls);
      }
    });
  });

  // Processar pastas
  selectedFolders.forEach(folder => {
    const checkbox = folder.querySelector('.folder-checkbox');
    const folderId = checkbox ? checkbox.value : null;
    if (folderId) {
      chrome.bookmarks.getChildren(folderId, (children) => {
        const folderBookmarks = children.filter(child => child.url);
        folderBookmarks.forEach(bookmark => allUrls.push(bookmark.url));
        processedCount++;
        if (processedCount === totalItems) {
          openUrlsInIncognito(allUrls);
        }
      });
    } else {
      processedCount++;
      if (processedCount === totalItems) {
        openUrlsInIncognito(allUrls);
      }
    }
  });
}

function handleOpenMultipleTabGroup() {
  showActionFeedback('Função de abrir múltiplos em grupo de guias em desenvolvimento', 'info');
}

/**
 * Funções auxiliares para abrir URLs
 */
function openUrlsInNewWindow(urls) {
  if (urls.length === 0) {
    showActionFeedback('Nenhum favorito encontrado para abrir', 'info');
    return;
  }

  chrome.windows.create({ url: urls }, () => {
    showActionFeedback(`${urls.length} favoritos abertos em nova janela`, 'success');
  });
}

function openUrlsInIncognito(urls) {
  if (urls.length === 0) {
    showActionFeedback('Nenhum favorito encontrado para abrir', 'info');
    return;
  }

  chrome.windows.create({ url: urls, incognito: true }, () => {
    showActionFeedback(`${urls.length} favoritos abertos em janela InPrivate`, 'success');
  });
}

/**
 * Função auxiliar para abrir todos os favoritos de uma pasta
 */
function openAllBookmarksInFolder(folderId, newWindow = false, incognito = false) {
  chrome.bookmarks.getChildren(folderId, (children) => {
    if (chrome.runtime.lastError) return;

    const bookmarks = children.filter(child => child.url);
    if (bookmarks.length === 0) {
      showActionFeedback('Nenhum favorito encontrado na pasta', 'info');
      return;
    }

    if (newWindow) {
      const urls = bookmarks.map(bookmark => bookmark.url);
      chrome.windows.create({ url: urls, incognito: incognito });
    } else {
      bookmarks.forEach(bookmark => {
        chrome.tabs.create({ url: bookmark.url });
      });
    }

    showActionFeedback(`${bookmarks.length} favoritos abertos`, 'success');
  });
}

/**
 * Mostra diálogo de edição de favorito
 */
function showEditBookmarkDialog(bookmarkId) {
  chrome.bookmarks.get(bookmarkId, (bookmarks) => {
    if (!bookmarks || !bookmarks[0]) return;

    const bookmark = bookmarks[0];
    const dialog = document.getElementById('editBookmarkDialog');
    const nameInput = document.getElementById('editBookmarkName');
    const urlInput = document.getElementById('editBookmarkUrl');

    nameInput.value = bookmark.title;
    urlInput.value = bookmark.url;

    dialog.style.display = 'flex';

    // Configurar botões
    document.getElementById('saveBookmarkBtn').onclick = () => {
      chrome.bookmarks.update(bookmarkId, {
        title: nameInput.value,
        url: urlInput.value
      }, () => {
        if (chrome.runtime.lastError) {
          showActionFeedback('Erro ao salvar favorito', 'error');
        } else {
          showActionFeedback('Favorito salvo com sucesso', 'success');
          dialog.style.display = 'none';
          if (typeof reloadSelectedFolders === 'function') {
            reloadSelectedFolders();
          }
        }
      });
    };

    document.getElementById('cancelEditBtn').onclick = () => {
      dialog.style.display = 'none';
    };

    document.getElementById('createNewFolderBtn').onclick = () => {
      showActionFeedback('Função de criar nova pasta em desenvolvimento', 'info');
    };
  });
}

/**
 * Move uma pasta para o topo da pasta pai
 */
function handleMoveFolderToTop() {
  if (!contextTarget) return;

  const folderOption = contextTarget.closest('.folder-option');
  if (!folderOption) return;

  const checkbox = folderOption.querySelector('.folder-checkbox');
  if (!checkbox) return;

  const folderId = checkbox.value;

  // Obter informações da pasta atual
  chrome.bookmarks.get(folderId, (folders) => {
    if (chrome.runtime.lastError || !folders || folders.length === 0) {
      showActionFeedback('Erro ao obter informações da pasta', 'error');
      return;
    }

    const folder = folders[0];
    const parentId = folder.parentId;

    // Mover para o índice 0 (topo)
    chrome.bookmarks.move(folderId, {
      parentId: parentId,
      index: 0
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Erro ao mover pasta para o topo:', chrome.runtime.lastError);
        showActionFeedback('Erro ao mover pasta para o topo', 'error');
      } else {
        showActionFeedback('Pasta movida para o topo com sucesso', 'success');

        // Recarregar lista de pastas
        if (typeof loadBookmarkFolders === 'function') {
          loadBookmarkFolders();
        } else {
          // Fallback: recarregar árvore de pastas
          chrome.bookmarks.getTree((tree) => {
            const roots = tree[0].children;
            const container = document.getElementById("folderCheckboxes");
            if (typeof populateFolderCheckboxes === 'function') {
              populateFolderCheckboxes(roots, container);
            }
          });
        }
      }
    });
  });
}

/**
 * Move um favorito para o topo da pasta atual
 */
function handleMoveBookmarkToTop() {
  if (!contextTarget) return;

  const bookmarkId = contextTarget.dataset.id;
  if (!bookmarkId) return;

  // Obter informações do favorito atual
  chrome.bookmarks.get(bookmarkId, (bookmarks) => {
    if (chrome.runtime.lastError || !bookmarks || bookmarks.length === 0) {
      showActionFeedback('Erro ao obter informações do favorito', 'error');
      return;
    }

    const bookmark = bookmarks[0];
    const parentId = bookmark.parentId;

    // Mover para o índice 0 (topo)
    chrome.bookmarks.move(bookmarkId, {
      parentId: parentId,
      index: 0
    }, () => {
      if (chrome.runtime.lastError) {
        console.error('Erro ao mover favorito para o topo:', chrome.runtime.lastError);
        showActionFeedback('Erro ao mover favorito para o topo', 'error');
      } else {
        showActionFeedback('Favorito movido para o topo com sucesso', 'success');

        // Recarregar favoritos da pasta atual
        if (typeof reloadSelectedFolders === 'function') {
          reloadSelectedFolders();
        } else {
          // Fallback: recarregar favoritos manualmente
          const selectedFolderElements = getSelectedFolderElements();
          selectedFolderElements.forEach(folderElement => {
            const checkbox = folderElement.querySelector('.folder-checkbox');
            if (checkbox && checkbox.checked) {
              const folderId = checkbox.value;
              chrome.bookmarks.getChildren(folderId, (children) => {
                if (!chrome.runtime.lastError) {
                  const items = children.filter(c => c.url);
                  renderBookmarks(items, bookmarksContainer, false, folderId, false);
                }
              });
            }
          });
        }
      }
    });
  });
}
