/**
 * Utilitários compartilhados para a extensão Bookmark Folder Merger.
 * Este arquivo contém funções auxiliares usadas em diferentes partes da extensão.
 */

/**
 * Extrai o domínio de uma URL
 * @param {string} url - URL completa
 * @returns {string} - Domínio extraído
 */
function extractDomain(url) {
  if (!url || typeof url !== "string") {
    console.error("URL inválida:", url);
    return "";
  }
  try {
    // Remover protocolo
    let domain = url.replace(/(https?:\/\/)?(www\.)?/i, '');
    
    // Obter apenas o domínio antes da primeira barra
    domain = domain.split('/')[0];
    
    // Remover porta se existir
    domain = domain.split(':')[0];
    
    return domain.toLowerCase();
  } catch (e) {
    console.error('Erro ao extrair domínio:', e);
    return url; // Retorna a URL original em caso de erro
  }
}

/**
 * Função para exibir feedback visual temporário de uma ação
 * @param {string} message - Mensagem a ser exibida
 * @param {string} type - Tipo de mensagem: 'success' (padrão), 'error', 'info'
 * @param {number} duration - Duração em milissegundos
 */
function showActionFeedback(message, type = 'success', duration = 2000) {
  let feedback = document.getElementById('action-feedback');
  
  // Criar o elemento se não existir
  if (!feedback) {
    feedback = document.createElement('div');
    feedback.id = 'action-feedback';
    document.body.appendChild(feedback);
    
    // Adicionar estilos inline para garantir que funcionem mesmo sem o CSS externo
    const baseStyles = `
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      padding: 12px 20px;
      border-radius: 4px;
      color: white;
      font-size: 14px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      z-index: 1100;
      opacity: 1;
      transition: opacity 0.3s ease;
      text-align: center;
      max-width: 80%;
      word-wrap: break-word;
    `;
    feedback.style.cssText = baseStyles;
  }
  
  // Limpar quaisquer timers existentes
  if (feedback.hideTimer) {
    clearTimeout(feedback.hideTimer);
  }
  
  // Definir cor com base no tipo
  let backgroundColor;
  switch (type) {
    case 'error':
      backgroundColor = '#e53935';
      break;
    case 'info':
      backgroundColor = '#1976d2';
      break;
    case 'warning':
      backgroundColor = '#ff9800';
      break;
    case 'success':
    default:
      backgroundColor = '#43a047';
      break;
  }
  
  // Aplicar estilos
  feedback.style.backgroundColor = backgroundColor;
  feedback.textContent = message;
  feedback.style.display = 'block';
  feedback.style.opacity = '1';
  
  // Esconder depois do tempo especificado
  feedback.hideTimer = setTimeout(() => {
    feedback.style.opacity = '0';
    setTimeout(() => {
      feedback.style.display = 'none';
    }, 300);
  }, duration);
}

/**
 * Adiciona e configura botões de limpar pesquisa nos campos de busca
 */
function setupClearSearchButtons() {
  const searchInputs = document.querySelectorAll('.search-input[data-show-clear="true"]');
  
  searchInputs.forEach(input => {
    const searchContainer = input.closest('.search-container');
    if (!searchContainer) return;

    // Remover botão existente se houver, para evitar duplicações
    const existingButton = searchContainer.querySelector('.clear-search-btn');
    if (existingButton) {
      existingButton.remove();
    }
    
    // Criar botão de limpar personalizado
    const clearButton = document.createElement('button');
    clearButton.className = 'clear-search-btn';
    clearButton.type = 'button';
    clearButton.title = 'Limpar pesquisa';
    clearButton.setAttribute('aria-label', 'Limpar campo de pesquisa');
    
    // Adicionar evento de clique para limpar o campo
    clearButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      
      // Limpar o campo
      input.value = '';
      input.focus();
      
      // Esconder o botão
      clearButton.style.display = 'none';
      
      // Disparar o evento input para atualizar a UI
      const inputEvent = new Event('input', { bubbles: true });
      input.dispatchEvent(inputEvent);
    });
    
    // Adicionar o botão ao container
    searchContainer.appendChild(clearButton);
    
    // Configurar exibição inicial com base no valor atual
    clearButton.style.display = input.value ? 'block' : 'none';
    
    // Atualizar visibilidade do botão com base no conteúdo do campo
    input.addEventListener('input', () => {
      clearButton.style.display = input.value ? 'block' : 'none';
    });
    
    // Garantir que o botão esteja visível quando o campo recebe foco e tem conteúdo
    input.addEventListener('focus', () => {
      if (input.value) {
        clearButton.style.display = 'block';
      }
    });
  });
}

/**
 * Reseta o estado de pesquisa e limpa os campos
 * @param {string} inputId - ID do campo de pesquisa a ser limpo
 */
function clearSearch(inputId) {
  const input = document.getElementById(inputId);
  if (!input) return;
  
  // Limpar o campo
  input.value = '';
  
  // Disparar o evento input para atualizar a UI
  const inputEvent = new Event('input', { bubbles: true });
  input.dispatchEvent(inputEvent);
  
  // Esconder o botão de limpar
  const clearButton = input.closest('.search-container')?.querySelector('.clear-search-btn');
  if (clearButton) {
    clearButton.style.display = 'none';
  }
  
  // Se for a pesquisa de favoritos e houver pastas selecionadas
  if (inputId === 'bookmarkSearch' && selectedFolderIds.size > 0) {
    // Verificar se não há favoritos visíveis ou se a última pesquisa não retornou resultados
    const visibleCount = Array.from(document.querySelectorAll(".bookmark-item"))
      .filter(item => item.style.display !== 'none').length;
    
    // Se não há favoritos visíveis ou a última pesquisa não retornou resultados, forçar recarga
    if (visibleCount === 0 || (typeof lastSearchHadNoResults !== 'undefined' && lastSearchHadNoResults)) {
      // Resetar a flag de pesquisa sem resultados (se existir no escopo)
      if (typeof lastSearchHadNoResults !== 'undefined') {
        lastSearchHadNoResults = false;
      }
      
      // Forçar recarga de favoritos
      loadSelectedBookmarks();
    }
  }
}

/**
 * Configura os manipuladores de eventos de dropdown
 * @param {string} btnId - ID do botão que abre o dropdown
 * @param {string} dropdownId - ID do elemento dropdown
 */
function setupDropdown(btnId, dropdownId) {
  const btn = document.getElementById(btnId);
  const dropdown = document.getElementById(dropdownId);
  
  if (!btn || !dropdown) return;
  
  // Configurar clique no botão para abrir dropdown
  btn.addEventListener("click", (e) => {
    e.stopPropagation();
    dropdown.classList.toggle("show");
    btn.classList.toggle("active");
  });
  
  // Fechar dropdown quando clicar fora dele
  document.addEventListener("click", (e) => {
    if (!e.target.matches(`#${btnId}`) && !dropdown.contains(e.target)) {
      dropdown.classList.remove("show");
      btn.classList.remove("active");
    }
  });
}

/**
 * Obtém o nome da pasta pelo ID
 * @param {string} folderId - ID da pasta
 * @returns {string|null} - Nome da pasta ou null se não encontrada
 */
function getFolderName(folderId) {
  if (!folderId) return null;
  
  const checkbox = document.querySelector(`input[type="checkbox"][value="${folderId}"]`);
  if (checkbox && checkbox.parentElement) {
    const folderText = checkbox.parentElement.textContent.trim();
    return folderText;
  }
  
  return null;
}

/**
 * Função para obter favoritos de uma pasta com tratamento de erros aprimorado
 * @param {string} folderId - ID da pasta
 * @returns {Promise<Array>} - Promise com os favoritos
 */
function getBookmarksPromise(folderId) {
  return new Promise((resolve, reject) => {
    try {
      chrome.bookmarks.getChildren(folderId, (bookmarks) => {
      if (chrome.runtime.lastError) {
          console.error(`Erro ao obter favoritos da pasta ${folderId}:`, chrome.runtime.lastError);
          reject(new Error(chrome.runtime.lastError.message || "Erro ao obter favoritos"));
          return;
}

        // Verificar se os favoritos são válidos
        if (!Array.isArray(bookmarks)) {
          console.error(`Formato inválido de favoritos recebidos para a pasta ${folderId}:`, bookmarks);
          reject(new Error("Formato inválido de favoritos recebidos"));
          return;
        }
        
        resolve(bookmarks);
      });
    } catch (error) {
      console.error(`Exceção ao obter favoritos da pasta ${folderId}:`, error);
      reject(error);
    }
  });
}

/**
 * Limpa o cache de elementos para reduzir o uso de memória
 * @param {boolean} force - Se true, limpa todo o cache; se false, limpa apenas itens não visíveis
 */
function pruneCache(force = false) {
  if (force) {
    // Limpar todo o cache
    bookmarkElementCache.clear();
    console.log("Cache de elementos limpo completamente");
    return;
  }
  
  // Obter IDs de favoritos atualmente visíveis
  const visibleIds = new Set();
  document.querySelectorAll(".bookmark-item").forEach(el => {
    if (el.dataset.id) {
      visibleIds.add(el.dataset.id);
    }
  });
    
  // Contar itens antes da limpeza
  const beforeCount = bookmarkElementCache.size;
  
  // Remover do cache itens que não estão visíveis
  for (const [id, element] of bookmarkElementCache.entries()) {
    if (!visibleIds.has(id)) {
      bookmarkElementCache.delete(id);
    }
  }
  
  // Contar itens após a limpeza
  const afterCount = bookmarkElementCache.size;
  const removedCount = beforeCount - afterCount;
  
  if (removedCount > 0) {
    console.log(`Cache de elementos reduzido: ${removedCount} itens removidos, ${afterCount} itens mantidos`);
  }
}

/**
 * Processa itens em lotes para evitar bloqueio da UI
 * @param {Array} items - Itens a serem processados
 * @param {Function} processFn - Função de processamento para cada item
 * @param {number} batchSize - Tamanho do lote
 * @returns {Promise} - Promise resolvida quando todos os itens forem processados
 */
function batchProcess(items, processFn, batchSize = 50) {
  return new Promise((resolve) => {
    const totalItems = items.length;
    let processedItems = 0;
    
    function processBatch(startIndex) {
      // Verificar se já processamos todos os itens
      if (startIndex >= totalItems) {
        resolve();
        return;
      }
      
      // Calcular o índice final do lote atual
      const endIndex = Math.min(startIndex + batchSize, totalItems);
      
      // Processar itens do lote atual
      for (let i = startIndex; i < endIndex; i++) {
        try {
          processFn(items[i], i);
        } catch (error) {
          console.error(`Erro ao processar item ${i}:`, error);
        }
        processedItems++;
      }
      
      // Agendar o próximo lote usando requestAnimationFrame para melhor performance
      requestAnimationFrame(() => {
        processBatch(endIndex);
        });
    }
    
    // Iniciar o processamento do primeiro lote
    processBatch(0);
  });
}

/**
 * Verifica se uma URL é potencialmente insegura
 * @param {string} url - URL a ser verificada
 * @returns {boolean} - true se a URL for potencialmente insegura
 */
function isUnsafeUrl(url) {
  if (!url || typeof url !== "string") {
    return true;
  }
  
  // Lista de protocolos seguros
  const safeProtocols = ["http:", "https:", "ftp:", "chrome:", "edge:", "about:", "browser:"];
  
  try {
    const parsedUrl = new URL(url);
    return !safeProtocols.includes(parsedUrl.protocol);
  } catch (error) {
    // Se não conseguir analisar a URL, considerar insegura
    return true;
  }
}

/**
 * Registra métricas de desempenho para diagnóstico
 * @param {string} label - Rótulo da métrica
 * @param {Function} fn - Função a ser medida
 * @returns {any} - Resultado da função
 */
function measurePerformance(label, fn) {
  console.time(label);
  try {
    return fn();
  } finally {
    console.timeEnd(label);
  }
}

/**
 * Verifica se há memória suficiente disponível
 * @returns {boolean} - true se há memória suficiente
 */
function checkMemoryAvailability() {
  if (window.performance && window.performance.memory) {
    const memory = window.performance.memory;
    const usedHeapSizeMB = memory.usedJSHeapSize / (1024 * 1024);
    const totalHeapSizeMB = memory.totalJSHeapSize / (1024 * 1024);
    const heapLimitMB = memory.jsHeapSizeLimit / (1024 * 1024);
    
    console.log(`Memória: ${usedHeapSizeMB.toFixed(2)}MB / ${totalHeapSizeMB.toFixed(2)}MB (Limite: ${heapLimitMB.toFixed(2)}MB)`);
    
    // Se estiver usando mais de 80% do limite, considerar pouca memória
    if (totalHeapSizeMB > heapLimitMB * 0.8) {
      console.warn("Pouca memória disponível, executando limpeza de cache");
      pruneCache(true);
      return false;
    }
  }
  
  return true;
}

// Intercepta clicks em links para prevenir navegação para URLs inseguras
document.addEventListener('DOMContentLoaded', () => {
  // Interceptar todas as navegações de links
  document.body.addEventListener('click', (e) => {
    const link = e.target.closest('a');
    if (!link || !link.href) return;
    
    // Verificar se o URL é seguro
    if (isUnsafeUrl(link.href)) {
      console.info("Info: Redirecionamento para URL interna do navegador bloqueado:", link.href);
      e.preventDefault();
      e.stopPropagation();
      
      // Reportar para o background script (apenas informativo)
      chrome.runtime.sendMessage({
        type: "INFO",
        error: `Redirecionamento para URL interna bloqueado: ${link.href}`
      }).catch(() => {
        // Ignorar erros de comunicação com o background script
      });
      
      // Mostrar mensagem ao usuário mais amigável
      alert("Esta URL é uma página interna do navegador que não pode ser aberta diretamente pela extensão. Você pode copiar e colar o endereço em uma nova aba.");
      return false;
    }
  }, true);
});