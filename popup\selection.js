/**
 * Sistema de Seleção de Favoritos
 * Gerencia toda a lógica de seleção, incluindo checkboxes, seleção múltipla,
 * clique direito, seleção com Shift e operações em favoritos selecionados
 */

// Variáveis globais para seleção de favoritos
let lastClickedBookmarkCheckbox = null;
const selectedBookmarkIds = new Set();

// Tornar variáveis acessíveis globalmente
window.selectedBookmarkIds = selectedBookmarkIds;

// Função para atualizar lastClickedBookmarkCheckbox globalmente
function updateLastClickedBookmarkCheckbox(checkbox) {
  lastClickedBookmarkCheckbox = checkbox;
  window.lastClickedBookmarkCheckbox = checkbox;
}

/**
 * Inicializa o sistema de seleção de favoritos
 */
function initBookmarkSelection() {
  console.log("Inicializando sistema de seleção de favoritos...");

  // Configurar botões de seleção
  setupSelectionButtons();

  // Configurar seleção com Shift
  setupBookmarkShiftSelection();

  // Clique direito removido - substituído por menu de contexto

  // Inicializar contador
  updateSelectedBookmarksCount();
}

/**
 * Configura os botões de selecionar/desselecionar todos
 */
function setupSelectionButtons() {
  // Botão "Selecionar todos"
  const selectAllBtn = document.getElementById("selectAllBookmarksBtn");
  if (selectAllBtn) {
    selectAllBtn.addEventListener("click", () => {
      toggleAllBookmarks(true);
    });
  }

  // Botão "Desselecionar todos"
  const deselectAllBtn = document.getElementById("deselectAllBookmarksBtn");
  if (deselectAllBtn) {
    deselectAllBtn.addEventListener("click", () => {
      toggleAllBookmarks(false);
    });
  }
}

// setupBookmarkLeftClickSelection removida - agora usa o comportamento automático do label+checkbox igual às pastas

/**
 * Atualiza o contador de favoritos selecionados
 */
function updateSelectedBookmarksCount() {
  const selectedCountEl = document.getElementById("selectedBookmarksCount");
  if (selectedCountEl) {
    selectedCountEl.textContent = `Selecionados: ${selectedBookmarkIds.size}`;
    
    // Adiciona classe visual para destacar quando há favoritos selecionados
    if (selectedBookmarkIds.size > 0) {
      selectedCountEl.classList.add("has-selected");
    } else {
      selectedCountEl.classList.remove("has-selected");
    }
  }
}

/**
 * Seleciona ou desseleciona todos os favoritos visíveis (igual às pastas)
 * @param {boolean} select - true para selecionar, false para desselecionar
 */
function toggleAllBookmarks(select) {
  const visibleCheckboxes = Array.from(document.querySelectorAll('.bookmark-checkbox'))
    .filter(checkbox => {
      const bookmarkItem = checkbox.closest('.bookmark-item');
      return bookmarkItem && bookmarkItem.style.display !== 'none';
    });

  if (visibleCheckboxes.length === 0) {
    showActionFeedback("Nenhum favorito visível para selecionar", "info");
    return;
  }

  // Captura quantos estavam selecionados antes de desmarcar
  let prevSelected = 0;
  if (!select) {
    prevSelected = visibleCheckboxes.filter(checkbox => checkbox.checked).length;
  }

  visibleCheckboxes.forEach(checkbox => {
    if (checkbox.checked !== select) {
      checkbox.checked = select;

      // Disparar o evento change para que os manipuladores sejam acionados (igual às pastas)
      const changeEvent = new Event('change', { bubbles: true });
      checkbox.dispatchEvent(changeEvent);
    }
  });

  // Se alguma checkbox foi selecionada, atualizar o último clicado
  if (visibleCheckboxes.length > 0) {
    updateLastClickedBookmarkCheckbox(visibleCheckboxes[0]);
  }

  // Mostrar feedback visual
  if (select) {
    showActionFeedback(`${visibleCheckboxes.length} favoritos selecionados`, "success");
  } else {
    showActionFeedback(`${prevSelected} favoritos desmarcados`, "info");
  }
}

// Função configureBookmarkRightClickSelection removida - substituída por menu de contexto

/**
 * Atualiza o estado de seleção de um favorito
 * @param {HTMLElement} bookmarkItem - Elemento do favorito
 * @param {boolean} selected - Estado de seleção
 */
function updateBookmarkSelection(bookmarkItem, selected) {
  const bookmarkId = bookmarkItem.dataset.id;
  
  if (selected) {
    selectedBookmarkIds.add(bookmarkId);
    bookmarkItem.classList.add("selected", "sortable-selected");
  } else {
    selectedBookmarkIds.delete(bookmarkId);
    bookmarkItem.classList.remove("selected", "sortable-selected");
  }
}

/**
 * Configura a seleção de favoritos usando a tecla Shift (igual às pastas)
 */
function setupBookmarkShiftSelection() {
  const bookmarksContainer = document.getElementById("bookmarksContainer");
  if (!bookmarksContainer) return;

  bookmarksContainer.addEventListener("click", (e) => {
    // Verificar se o clique foi em um item de favorito (label) e se Shift está pressionado
    const bookmarkItem = e.target.closest(".bookmark-item");
    if (!bookmarkItem || !e.shiftKey) return;

    const currentCheckbox = bookmarkItem.querySelector(".bookmark-checkbox");
    if (!currentCheckbox) return;

    // Se há um checkbox anterior clicado, fazer seleção em range
    if (lastClickedBookmarkCheckbox) {
      // Obter todos os checkboxes visíveis
      const checkboxes = Array.from(document.querySelectorAll(".bookmark-checkbox"))
        .filter(cb => {
          // Verificar se o checkbox está visível
          const container = cb.closest(".bookmark-item");
          return container && window.getComputedStyle(container).display !== "none";
        });

      // Encontrar índices do checkbox atual e do último clicado
      const currentIndex = checkboxes.indexOf(currentCheckbox);
      const lastIndex = checkboxes.indexOf(lastClickedBookmarkCheckbox);

      if (currentIndex !== -1 && lastIndex !== -1) {
        // Determinar o range de seleção
        const startIndex = Math.min(currentIndex, lastIndex);
        const endIndex = Math.max(currentIndex, lastIndex);

        // Determinar o estado de seleção baseado no checkbox atual (que será alternado pelo label)
        const selectState = !currentCheckbox.checked; // Será o estado após o toggle

        // Aplicar o estado a todos os checkboxes no range
        for (let i = startIndex; i <= endIndex; i++) {
          const checkbox = checkboxes[i];
          const item = checkbox.closest('.bookmark-item');

          if (item && checkbox.checked !== selectState) {
            checkbox.checked = selectState;
            updateBookmarkSelection(item, selectState);
          }
        }

        updateSelectedBookmarksCount();
      }
    }

    // Atualizar o último checkbox clicado (será atualizado após o toggle automático)
    setTimeout(() => {
      updateLastClickedBookmarkCheckbox(currentCheckbox);
    }, 0);
  });
}

/**
 * Limpa todas as seleções de favoritos
 */
function clearBookmarkSelection() {
  selectedBookmarkIds.clear();
  
  // Remover classes visuais de seleção
  document.querySelectorAll('.bookmark-item.selected').forEach(item => {
    item.classList.remove('selected', 'sortable-selected');
  });
  
  // Desmarcar todos os checkboxes
  document.querySelectorAll('.bookmark-checkbox:checked').forEach(checkbox => {
    checkbox.checked = false;
  });
  
  updateSelectedBookmarksCount();
}

/**
 * Obtém os IDs dos favoritos selecionados
 * @returns {Array} Array com os IDs dos favoritos selecionados
 */
function getSelectedBookmarkIds() {
  return Array.from(selectedBookmarkIds);
}

/**
 * Obtém os elementos DOM dos favoritos selecionados
 * @returns {Array} Array com os elementos DOM dos favoritos selecionados
 */
function getSelectedBookmarkElements() {
  return getSelectedBookmarkIds().map(id => 
    document.querySelector(`.bookmark-item[data-id="${id}"]`)
  ).filter(element => element !== null);
}

/**
 * Verifica se há favoritos selecionados
 * @returns {boolean} true se há favoritos selecionados
 */
function hasSelectedBookmarks() {
  return selectedBookmarkIds.size > 0;
}

/**
 * Seleciona favoritos por IDs
 * @param {Array} bookmarkIds - Array de IDs de favoritos para selecionar
 */
function selectBookmarksByIds(bookmarkIds) {
  bookmarkIds.forEach(id => {
    const bookmarkItem = document.querySelector(`.bookmark-item[data-id="${id}"]`);
    if (bookmarkItem) {
      const checkbox = bookmarkItem.querySelector('.bookmark-checkbox');
      if (checkbox) {
        checkbox.checked = true;
        updateBookmarkSelection(bookmarkItem, true);
      }
    }
  });
  
  updateSelectedBookmarksCount();
}
