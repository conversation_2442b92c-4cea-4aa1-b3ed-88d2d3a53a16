/**
 * Sistema de Debug para Favicons
 * 
 * Ferramentas para debug e monitoramento do sistema de favicons
 */

window.FaviconDebug = window.FaviconDebug || {};

(function() {
    'use strict';

    /**
     * Testa o carregamento de favicon para uma URL específica
     * @param {string} url - URL para testar
     */
    function testFaviconLoad(url) {
        console.log(`[FaviconDebug] Testando carregamento de favicon para: ${url}`);

        // Mostrar URL gerada
        if (window.FaviconSystem?.getFaviconUrl) {
            const faviconUrl = window.FaviconSystem.getFaviconUrl(url);
            console.log(`[FaviconDebug] URL do favicon gerada: ${faviconUrl}`);
        }

        // Criar elemento de teste
        const testImg = document.createElement('img');
        testImg.style.position = 'fixed';
        testImg.style.top = '10px';
        testImg.style.right = '10px';
        testImg.style.width = '32px';
        testImg.style.height = '32px';
        testImg.style.border = '2px solid red';
        testImg.style.zIndex = '9999';
        testImg.style.backgroundColor = 'white';
        testImg.title = url;

        document.body.appendChild(testImg);

        // Carregar favicon
        if (window.FaviconSystem?.loadFavicon) {
            window.FaviconSystem.loadFavicon(testImg, url);

            // Remover após 10 segundos
            setTimeout(() => {
                if (testImg.parentNode) {
                    testImg.parentNode.removeChild(testImg);
                }
            }, 10000);
        } else {
            console.error('[FaviconDebug] FaviconSystem não disponível');
            testImg.src = '../img/icons/star_blue_ext.png';
        }
    }

    /**
     * Testa diretamente a API da extensão
     * @param {string} url - URL para testar
     */
    function testExtensionFaviconAPI(url) {
        const extensionId = chrome.runtime?.id || 'kmgmgcmhnogndogmglaikkkjadhhinbm';
        const faviconUrl = `chrome-extension://${extensionId}/_favicon/?pageUrl=${encodeURIComponent(url)}&size=16`;

        console.log(`[FaviconDebug] Testando API direta da extensão:`);
        console.log(`[FaviconDebug] Extension ID: ${extensionId}`);
        console.log(`[FaviconDebug] URL original: ${url}`);
        console.log(`[FaviconDebug] URL do favicon: ${faviconUrl}`);

        // Criar elemento de teste
        const testImg = document.createElement('img');
        testImg.style.position = 'fixed';
        testImg.style.top = '50px';
        testImg.style.right = '10px';
        testImg.style.width = '32px';
        testImg.style.height = '32px';
        testImg.style.border = '2px solid blue';
        testImg.style.zIndex = '9999';
        testImg.style.backgroundColor = 'white';
        testImg.title = `API Test: ${url}`;

        testImg.onload = () => {
            console.log(`[FaviconDebug] ✅ Favicon carregado com sucesso via API da extensão`);
        };

        testImg.onerror = () => {
            console.log(`[FaviconDebug] ❌ Erro ao carregar favicon via API da extensão`);
        };

        testImg.src = faviconUrl;
        document.body.appendChild(testImg);

        // Remover após 10 segundos
        setTimeout(() => {
            if (testImg.parentNode) {
                testImg.parentNode.removeChild(testImg);
            }
        }, 10000);
    }

    /**
     * Mostra estatísticas do sistema de favicons
     */
    function showStats() {
        if (window.FaviconSystem?.getCacheStats) {
            const stats = window.FaviconSystem.getCacheStats();
            console.table(stats);
        } else {
            console.error('[FaviconDebug] FaviconSystem não disponível');
        }
    }

    /**
     * Testa URLs comuns
     */
    function testCommonUrls() {
        const testUrls = [
            'https://www.google.com',
            'https://www.github.com',
            'https://www.stackoverflow.com',
            'https://www.youtube.com',
            'https://www.facebook.com'
        ];

        testUrls.forEach((url, index) => {
            setTimeout(() => {
                testFaviconLoad(url);
            }, index * 2000);
        });
    }

    /**
     * Verifica se o sistema está funcionando
     */
    function checkSystem() {
        console.log('[FaviconDebug] Verificando sistema de favicons...');
        
        const checks = {
            faviconSystemLoaded: typeof window.FaviconSystem !== 'undefined',
            faviconCacheLoaded: typeof window.FaviconCache !== 'undefined',
            faviconLoaderLoaded: typeof window.FaviconLoader !== 'undefined',
            systemInitialized: window.FaviconSystem?.getCacheStats ? 
                window.FaviconSystem.getCacheStats().initialized : false
        };
        
        console.table(checks);
        
        if (checks.faviconSystemLoaded) {
            showStats();
        }
        
        return checks;
    }

    /**
     * Monitora elementos de favicon na página
     */
    function monitorFaviconElements() {
        const faviconElements = document.querySelectorAll('.favicon');
        console.log(`[FaviconDebug] Encontrados ${faviconElements.length} elementos de favicon`);
        
        faviconElements.forEach((element, index) => {
            console.log(`[FaviconDebug] Favicon ${index + 1}:`, {
                src: element.src,
                alt: element.alt,
                width: element.width,
                height: element.height,
                parentElement: element.parentElement?.className
            });
        });
    }

    /**
     * Força o recarregamento de todos os favicons visíveis
     */
    function reloadAllFavicons() {
        const bookmarkItems = document.querySelectorAll('.bookmark-item');
        console.log(`[FaviconDebug] Recarregando favicons para ${bookmarkItems.length} bookmarks`);
        
        bookmarkItems.forEach((item, index) => {
            const favicon = item.querySelector('.favicon');
            const url = item.querySelector('.bookmark-link')?.href;
            
            if (favicon && url && window.FaviconSystem?.loadFavicon) {
                setTimeout(() => {
                    console.log(`[FaviconDebug] Recarregando favicon ${index + 1}: ${url}`);
                    window.FaviconSystem.loadFavicon(favicon, url);
                }, index * 100);
            }
        });
    }

    // API pública
    window.FaviconDebug = {
        testFaviconLoad,
        testExtensionFaviconAPI,
        showStats,
        testCommonUrls,
        checkSystem,
        monitorFaviconElements,
        reloadAllFavicons
    };

    // Auto-verificação quando carregado
    setTimeout(() => {
        console.log('[FaviconDebug] Sistema de debug carregado');
        checkSystem();
    }, 1000);

})();

// Adicionar comandos globais para debug no console
window.debugFavicons = () => window.FaviconDebug.checkSystem();
window.testFavicons = () => window.FaviconDebug.testCommonUrls();
window.reloadFavicons = () => window.FaviconDebug.reloadAllFavicons();
window.testExtensionAPI = (url = 'https://www.google.com') => window.FaviconDebug.testExtensionFaviconAPI(url);
