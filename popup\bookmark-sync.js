/**
 * Sistema de Sincronização de Bookmarks
 * 
 * Gerencia a sincronização da interface quando bookmarks são alterados
 * externamente (pelo navegador ou outras extensões).
 */

window.BookmarkSync = window.BookmarkSync || {};

(function() {
    'use strict';

    // Estado do sistema
    let isInitialized = false;
    let updateQueue = [];
    let isProcessingQueue = false;

    /**
     * Inicializa o sistema de sincronização
     */
    function init() {
        if (isInitialized) return;

        console.log('[BookmarkSync] Inicializando sistema de sincronização...');

        // Listener para mensagens do background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('[BookmarkSync] Mensagem recebida:', message);
            if (message.type === 'BOOKMARK_CHANGE') {
                console.log('[BookmarkSync] Processando mudança de bookmark:', message.changeType, message.data);
                handleBookmarkChange(message.changeType, message.data);
            }
        });

        isInitialized = true;
        console.log('[BookmarkSync] Sistema de sincronização inicializado');
    }

    /**
     * Processa mudanças nos bookmarks
     * @param {string} changeType - Tipo de mudança
     * @param {Object} data - Dados da mudança
     */
    function handleBookmarkChange(changeType, data) {
        console.log(`[BookmarkSync] Mudança detectada: ${changeType}`, data);

        // Adicionar à fila de atualizações
        updateQueue.push({ changeType, data, timestamp: Date.now() });

        // Processar fila
        processUpdateQueue();
    }

    /**
     * Processa a fila de atualizações
     */
    async function processUpdateQueue() {
        if (isProcessingQueue || updateQueue.length === 0) return;

        isProcessingQueue = true;

        try {
            while (updateQueue.length > 0) {
                const update = updateQueue.shift();
                await processUpdate(update);
                
                // Pequeno delay para não sobrecarregar a interface
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        } catch (error) {
            console.error('[BookmarkSync] Erro ao processar fila de atualizações:', error);
        } finally {
            isProcessingQueue = false;
        }
    }

    /**
     * Processa uma atualização individual
     * @param {Object} update - Atualização para processar
     */
    async function processUpdate(update) {
        const { changeType, data } = update;

        switch (changeType) {
            case 'removed':
                await handleRemoved(data);
                break;
            case 'created':
                await handleCreated(data);
                break;
            case 'changed':
                await handleChanged(data);
                break;
            case 'moved':
                await handleMoved(data);
                break;
            default:
                console.warn(`[BookmarkSync] Tipo de mudança desconhecido: ${changeType}`);
        }
    }

    /**
     * Trata remoção de bookmark/pasta
     * @param {Object} data - Dados da remoção
     */
    async function handleRemoved(data) {
        const { id, node } = data;
        
        console.log(`[BookmarkSync] Processando remoção: ${id}`);

        // Se foi uma pasta que foi removida
        if (node && !node.url) {
            await handleFolderRemoved(id);
        } else {
            // Se foi um bookmark que foi removido
            await handleBookmarkRemoved(id);
        }
    }

    /**
     * Trata remoção de pasta
     * @param {string} folderId - ID da pasta removida
     */
    async function handleFolderRemoved(folderId) {
        console.log(`[BookmarkSync] Pasta removida: ${folderId}`);

        // Remover da lista de pastas selecionadas se estiver lá
        if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.has(folderId)) {
            selectedFolderIds.delete(folderId);
            
            // Atualizar contador
            const selectedCountEl = document.getElementById("selectedFoldersCount");
            if (selectedCountEl) {
                selectedCountEl.textContent = `Selecionadas: ${selectedFolderIds.size}`;
                if (selectedFolderIds.size === 0) {
                    selectedCountEl.classList.remove("has-selected");
                }
            }
        }

        // Remover elemento da interface
        const folderElement = document.querySelector(`input[value="${folderId}"]`);
        if (folderElement) {
            const folderOption = folderElement.closest('.folder-option');
            if (folderOption) {
                folderOption.remove();
                console.log(`[BookmarkSync] Elemento da pasta ${folderId} removido da interface`);
            }
        }

        // Remover favoritos desta pasta da coluna 2
        const bookmarkElements = document.querySelectorAll(`[data-folder="${folderId}"]`);
        bookmarkElements.forEach(element => element.remove());

        // Limpar cache relacionado
        if (typeof folderBookmarkMap !== 'undefined') {
            folderBookmarkMap.delete(folderId);
        }

        // Atualizar contadores
        updateFolderCount();
        updateBookmarkCount();

        showActionFeedback('Pasta removida externamente', 'info');
    }

    /**
     * Trata remoção de bookmark
     * @param {string} bookmarkId - ID do bookmark removido
     */
    async function handleBookmarkRemoved(bookmarkId) {
        console.log(`[BookmarkSync] Bookmark removido: ${bookmarkId}`);

        // Remover elemento da interface
        const bookmarkElement = document.querySelector(`[data-id="${bookmarkId}"]`);
        if (bookmarkElement) {
            bookmarkElement.remove();
            console.log(`[BookmarkSync] Elemento do bookmark ${bookmarkId} removido da interface`);
        }

        // Remover do cache
        if (typeof bookmarkElementCache !== 'undefined') {
            bookmarkElementCache.delete(bookmarkId);
        }

        // Remover da seleção se estiver selecionado
        if (typeof selectedBookmarkIds !== 'undefined') {
            selectedBookmarkIds.delete(bookmarkId);
        }

        // Atualizar contadores
        updateBookmarkCount();
        if (typeof updateSelectedBookmarksCount === 'function') {
            updateSelectedBookmarksCount();
        }
    }

    /**
     * Trata criação de bookmark/pasta
     * @param {Object} data - Dados da criação
     */
    async function handleCreated(data) {
        const { bookmark } = data;
        
        console.log(`[BookmarkSync] Item criado: ${bookmark.id}`);

        // Se foi uma pasta criada, recarregar lista de pastas
        if (!bookmark.url) {
            await reloadFolderList();
            showActionFeedback('Nova pasta detectada', 'info');
        } else {
            // Se foi um bookmark criado, verificar se está em pasta selecionada
            if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.has(bookmark.parentId)) {
                await reloadSelectedFolders();
                showActionFeedback('Novo bookmark detectado', 'info');
            }
        }
    }

    /**
     * Trata alteração de bookmark/pasta
     * @param {Object} data - Dados da alteração
     */
    async function handleChanged(data) {
        const { id, title, url } = data;
        
        console.log(`[BookmarkSync] Item alterado: ${id}`);

        // Se foi uma pasta alterada
        if (!url) {
            // Atualizar título da pasta na interface
            const folderCheckbox = document.querySelector(`input[value="${id}"]`);
            if (folderCheckbox) {
                const label = folderCheckbox.parentElement;
                if (label) {
                    // Manter apenas o checkbox e atualizar o texto
                    const textNode = Array.from(label.childNodes).find(node => node.nodeType === Node.TEXT_NODE);
                    if (textNode) {
                        textNode.textContent = title;
                    }
                }
            }
            showActionFeedback('Pasta renomeada externamente', 'info');
        } else {
            // Se foi um bookmark alterado
            const bookmarkElement = document.querySelector(`[data-id="${id}"]`);
            if (bookmarkElement) {
                // Atualizar título e URL
                const titleElement = bookmarkElement.querySelector('.bookmark-link');
                const urlElement = bookmarkElement.querySelector('.url-container');
                
                if (titleElement) titleElement.textContent = title;
                if (urlElement) urlElement.textContent = url;
                
                // Recarregar favicon se a URL mudou
                if (url) {
                    const favicon = bookmarkElement.querySelector('.favicon');
                    if (favicon && window.FaviconSystem?.loadFavicon) {
                        window.FaviconSystem.loadFavicon(favicon, url);
                    }
                }
            }
            showActionFeedback('Bookmark alterado externamente', 'info');
        }
    }

    /**
     * Trata movimentação de bookmark/pasta
     * @param {Object} data - Dados da movimentação
     */
    async function handleMoved(data) {
        console.log(`[BookmarkSync] Item movido: ${data.id}`);

        // Para movimentações, é mais seguro recarregar tudo
        await reloadFolderList();
        
        if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.size > 0) {
            await reloadSelectedFolders();
        }

        showActionFeedback('Item movido externamente', 'info');
    }

    /**
     * Recarrega a lista de pastas
     */
    async function reloadFolderList() {
        if (typeof loadBookmarkFolders === 'function') {
            loadBookmarkFolders();
        } else {
            // Fallback
            chrome.bookmarks.getTree((tree) => {
                const roots = tree[0].children;
                const container = document.getElementById("folderCheckboxes");
                if (typeof populateFolderCheckboxes === 'function') {
                    populateFolderCheckboxes(roots, container);
                }
                updateFolderCount();
            });
        }
    }

    /**
     * Recarrega favoritos das pastas selecionadas
     */
    async function reloadSelectedFolders() {
        if (typeof reloadSelectedFolders === 'function') {
            reloadSelectedFolders(true); // Preservar scroll
        }
    }

    /**
     * Força uma sincronização completa
     */
    async function forceSync() {
        console.log('[BookmarkSync] Forçando sincronização completa...');
        
        await reloadFolderList();
        
        if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.size > 0) {
            await reloadSelectedFolders();
        }
        
        showActionFeedback('Sincronização completa realizada', 'success');
    }

    /**
     * Obtém estatísticas do sistema
     */
    function getStats() {
        return {
            initialized: isInitialized,
            queueSize: updateQueue.length,
            processing: isProcessingQueue
        };
    }

    // API pública
    window.BookmarkSync = {
        init,
        forceSync,
        getStats
    };

    // Auto-inicializar quando o DOM estiver pronto
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 0);
    }

    // Comando global para debug
    window.forceBookmarkSync = () => forceSync();

    console.log('[BookmarkSync] Sistema carregado');

})();
