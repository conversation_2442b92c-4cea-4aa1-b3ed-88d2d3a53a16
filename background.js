// Listener para comandos do teclado
chrome.commands.onCommand.addListener((command) => {
    if (command === "inject-script") {
        handleInjectScriptCommand();
    }
});

// Função para lidar com o comando de injeção de script
function handleInjectScriptCommand() {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs.length === 0) return;

        const currentTab = tabs[0];
        const url = currentTab.url || "";

        if (isInternalBrowserUrl(url)) {
            console.info("Não é possível injetar script em URL interna do navegador:", url);
            return;
        }

        injectScriptIntoTab(currentTab.id);
    });
}

// Verifica se a URL é interna do navegador
function isInternalBrowserUrl(url) {
    const internalPrefixes = [
        "chrome://",
        "edge://",
        "about:",
        "browser://",
        "chrome-extension://"
    ];
    return internalPrefixes.some((prefix) => url.startsWith(prefix));
}

// Injeta o script no tab especificado
function injectScriptIntoTab(tabId) {
    chrome.scripting.executeScript({
        target: { tabId },
        files: ["content.js"]
    }).catch((err) => {
        console.error("Erro ao executar script:", err);
    });
}

// Configurações padrão da extensão
const defaultSettings = {
    fontSize: 'medium',
    confirmDelete: true,
    confirmMerge: true,
    favoriteFolders: []
};

// Configurações padrão de tema
const defaultThemeSettings = {
    light: {
        name: 'Tema Claro',
        states: {
            normal: {
                name: 'Normal',
                background: { name: 'Fundo', enabled: true, color: '#ffffff', opacity: 1 },
                border: { name: 'Borda', enabled: true, color: '#dddddd', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#000000', opacity: 0.1, size: 2 }
            },
            selected: {
                name: 'Selecionado',
                background: { name: 'Fundo', enabled: true, color: '#efeef6', opacity: 1 },
                border: { name: 'Borda', enabled: false, color: '#000000', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#000000', opacity: 0.1, size: 2 }
            },
            hover: {
                name: 'Hover',
                background: { name: 'Fundo', enabled: true, color: '#e0dfe7', opacity: 1 },
                border: { name: 'Borda', enabled: false, color: '#000000', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#000000', opacity: 0.1, size: 2 },
                underline: { name: 'Underline', enabled: true, thickness: 1, opacity: 0.55, color: '#333333' }
            }
        }
    },
    dark: {
        name: 'Tema Escuro',
        states: {
            normal: {
                name: 'Normal',
                background: { name: 'Fundo', enabled: true, color: '#2a2a2e', opacity: 1 },
                border: { name: 'Borda', enabled: true, color: '#444444', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#000000', opacity: 0.2, size: 2 }
            },
            selected: {
                name: 'Selecionado',
                background: { name: 'Fundo', enabled: true, color: '#4a4a50', opacity: 1 },
                border: { name: 'Borda', enabled: false, color: '#ffffff', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#ffffff', opacity: 0.1, size: 2 },
            },
            hover: {
                name: 'Hover',
                background: { name: 'Fundo', enabled: true, color: '#686576', opacity: 1 },
                border: { name: 'Borda', enabled: false, color: '#ffffff', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#ffffff', opacity: 0.1, size: 2 },
                underline: { name: 'Underline', enabled: true, thickness: 1, opacity: 0.55, color: '#e0e0e0' }
            }
        }
    }
};

// Inicializar a extensão quando for instalada ou atualizada
chrome.runtime.onInstalled.addListener(({ reason }) => {
    // Inicializar configurações
    if (reason === 'install') {
        initializeSettings();
    } else if (reason === 'update') {
        updateSettings();
    }

    
    // Criar menu de contexto
    createContextMenu();
});

// Função para criar o menu de contexto
function createContextMenu() {
    // Remover itens existentes para evitar duplicações
    chrome.contextMenus.removeAll(() => {
        // Criar o item de menu de contexto
        chrome.contextMenus.create({
            id: "open-extension-tab",
            title: "Abrir Bookmark Folder Merger em uma aba",
            contexts: ["action"], // Aparece no menu de contexto do ícone da extensão
        });
    });
}

// Listener para cliques no menu de contexto
chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === "open-extension-tab") {
        openExtensionInTab();
    }
});

// ===== LISTENERS PARA MUDANÇAS NOS BOOKMARKS =====

/**
 * Notifica todas as abas abertas da extensão sobre mudanças nos bookmarks
 * @param {string} changeType - Tipo de mudança (removed, created, changed, moved)
 * @param {Object} data - Dados da mudança
 */
function notifyBookmarkChange(changeType, data) {
    console.log(`[Background] Notificando mudança: ${changeType}`, data);

    // Enviar mensagem para todas as abas que podem ter a extensão aberta
    chrome.tabs.query({}, (tabs) => {
        console.log(`[Background] Enviando para ${tabs.length} abas`);
        tabs.forEach(tab => {
            try {
                chrome.tabs.sendMessage(tab.id, {
                    type: 'BOOKMARK_CHANGE',
                    changeType: changeType,
                    data: data
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        // Ignorar erros para abas que não têm a extensão
                        console.log(`[Background] Erro ao enviar para aba ${tab.id}:`, chrome.runtime.lastError.message);
                    } else {
                        console.log(`[Background] Mensagem enviada com sucesso para aba ${tab.id}`);
                    }
                });
            } catch (e) {
                console.log(`[Background] Exceção ao enviar para aba ${tab.id}:`, e);
            }
        });
    });
}

// Listener para quando um bookmark ou pasta é removido
chrome.bookmarks.onRemoved.addListener((id, removeInfo) => {
    console.log('[Background] Bookmark/Pasta removido:', id, removeInfo);
    notifyBookmarkChange('removed', {
        id: id,
        parentId: removeInfo.parentId,
        index: removeInfo.index,
        node: removeInfo.node
    });
});

// Listener para quando um bookmark ou pasta é criado
chrome.bookmarks.onCreated.addListener((id, bookmark) => {
    notifyBookmarkChange('created', {
        id: id,
        bookmark: bookmark
    });
});

// Listener para quando um bookmark ou pasta é alterado
chrome.bookmarks.onChanged.addListener((id, changeInfo) => {
    notifyBookmarkChange('changed', {
        id: id,
        title: changeInfo.title,
        url: changeInfo.url
    });
});

// Listener para quando um bookmark ou pasta é movido
chrome.bookmarks.onMoved.addListener((id, moveInfo) => {
    notifyBookmarkChange('moved', {
        id: id,
        parentId: moveInfo.parentId,
        index: moveInfo.index,
        oldParentId: moveInfo.oldParentId,
        oldIndex: moveInfo.oldIndex
    });
});

// Escutar mensagens de outras partes da extensão
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // Verificar se é uma mensagem de configuração
    if (message.action === 'getSettings') {
        // Retornar as configurações atuais
        getSettings().then(settings => {
            sendResponse({ success: true, settings });
        });
        return true; // Indica que a resposta será assíncrona
    }
    
    if (message.action === 'saveSettings') {
        // Salvar novas configurações
        saveSettings(message.settings).then(() => {
            sendResponse({ success: true });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true; // Indica que a resposta será assíncrona
    }

    if (message.action === 'configUpdated') {
        // Enviar a atualização para todas as abas abertas
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
                try {
                    chrome.tabs.sendMessage(tab.id, message);
                } catch (e) {
                    // Se falhar para alguma aba, não há problema, só ignorar
                }
            });
        });
        
        // Salvar configurações recebidas
        if (message.settings) {
            saveSettings(message.settings)
                .then(() => {
                    console.log('Configurações atualizadas em background.js');
                    sendResponse({ success: true });
                })
                .catch(err => {
                    console.error('Erro ao salvar configurações:', err);
                    sendResponse({ success: false, error: err.message });
                });
            
            return true; // Indica que a resposta será assíncrona
        }
    }
    
    // Verificar mensagens comuns
    switch (message.type) {
        case "ERROR":
            console.error("Erro reportado:", message.error);
            break;
        case "INFO":
            console.info("Informação:", message.error || message.info || message.message);
            break;
        case "WARNING":
            console.warn("Aviso:", message.warning || message.message);
            break;
        case "SAVE_BOOKMARK":
            handleSaveBookmark(message.data, sendResponse);
            return true; // Manter o canal de mensagem aberto para resposta assíncrona
        case "OPEN_POPUP":
            handleOpenPopup();
            break;
        case "OPEN_IN_TAB":
            openExtensionInTab();
            break;
        default:
            console.warn("Tipo de mensagem desconhecido:", message);
    }
    
    return true;
});

// Inicializar as configurações com valores padrão
async function initializeSettings() {
    console.log('Inicializando configurações da extensão...');

    try {
        // Verificar se as configurações já existem
        const existingSettings = await getSettings();
        const existingThemeSettings = await getThemeSettings();

        // Inicializar configurações gerais se não existirem
        if (!existingSettings || Object.keys(existingSettings).length === 0) {
            await saveSettings(defaultSettings);
            console.log('Configurações padrão inicializadas:', defaultSettings);
        } else {
            console.log('Configurações gerais já existem:', existingSettings);
        }

        // Inicializar configurações de tema se não existirem
        if (!existingThemeSettings || Object.keys(existingThemeSettings).length === 0) {
            await saveThemeSettings(defaultThemeSettings);
            console.log('Configurações de tema padrão inicializadas:', defaultThemeSettings);
        } else {
            console.log('Configurações de tema já existem:', existingThemeSettings);
        }

    } catch (error) {
        console.error('Erro ao inicializar configurações:', error);
    }
}

// Atualizar configurações existentes para garantir compatibilidade com novas versões
async function updateSettings() {
    console.log('Atualizando configurações da extensão...');

    try {
        // Obter configurações atuais
        const currentSettings = await getSettings();
        const currentThemeSettings = await getThemeSettings();

        // Atualizar configurações gerais
        if (!currentSettings) {
            await saveSettings(defaultSettings);
        } else {
            // Mesclar configurações atuais com padrões para garantir que todas as chaves existam
            const updatedSettings = {
                ...defaultSettings,
                ...currentSettings
            };
            await saveSettings(updatedSettings);
            console.log('Configurações gerais atualizadas:', updatedSettings);
        }

        // Atualizar configurações de tema
        if (!currentThemeSettings) {
            await saveThemeSettings(defaultThemeSettings);
            console.log('Configurações de tema padrão aplicadas na atualização');
        } else {
            // Verificar se todas as propriedades de tema existem e adicionar as que faltam
            const updatedThemeSettings = {
                ...defaultThemeSettings,
                ...currentThemeSettings
            };

            // Garantir que cada tema tenha todas as propriedades necessárias
            ['light', 'dark'].forEach(themeKey => {
                if (!updatedThemeSettings[themeKey]) {
                    updatedThemeSettings[themeKey] = defaultThemeSettings[themeKey];
                } else {
                    // Garantir que todos os estados existam
                    ['normal', 'selected', 'hover'].forEach(stateKey => {
                        if (!updatedThemeSettings[themeKey].states[stateKey]) {
                            updatedThemeSettings[themeKey].states[stateKey] = defaultThemeSettings[themeKey].states[stateKey];
                        }
                    });
                }
            });

            await saveThemeSettings(updatedThemeSettings);
            console.log('Configurações de tema atualizadas:', updatedThemeSettings);
        }

    } catch (error) {
        console.error('Erro ao atualizar configurações:', error);
    }
}

// Obter as configurações atuais
async function getSettings() {
    return new Promise((resolve) => {
        // Primeiro tentar obter do sync storage
        chrome.storage.sync.get('settings', (result) => {
            if (chrome.runtime.lastError || !result.settings) {
                console.warn('Não foi possível obter configurações do sync storage, tentando local storage');
                
                // Tentar obter do local storage como fallback
                chrome.storage.local.get('settings', (localResult) => {
                    resolve(localResult.settings || null);
                });
            } else {
                resolve(result.settings);
            }
        });
    });
}

// Salvar configurações
async function saveSettings(settings) {
    return new Promise((resolve, reject) => {
        // Primeiro tentar salvar no sync storage
        chrome.storage.sync.set({ settings }, () => {
            if (chrome.runtime.lastError) {
                console.warn('Não foi possível salvar no sync storage, tentando local storage');

                // Tentar salvar no local storage como fallback
                chrome.storage.local.set({ settings }, () => {
                    if (chrome.runtime.lastError) {
                        reject(new Error('Não foi possível salvar configurações: ' + chrome.runtime.lastError.message));
                    } else {
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    });
}

// Obter configurações de tema
async function getThemeSettings() {
    return new Promise((resolve) => {
        // Primeiro tentar obter do sync storage
        chrome.storage.sync.get('themeSettings', (result) => {
            if (chrome.runtime.lastError || !result.themeSettings) {
                console.warn('Não foi possível obter configurações de tema do sync storage, tentando local storage');

                // Tentar obter do local storage como fallback
                chrome.storage.local.get('themeSettings', (localResult) => {
                    resolve(localResult.themeSettings || null);
                });
            } else {
                resolve(result.themeSettings);
            }
        });
    });
}

// Salvar configurações de tema
async function saveThemeSettings(themeSettings) {
    return new Promise((resolve, reject) => {
        // Primeiro tentar salvar no sync storage
        chrome.storage.sync.set({ themeSettings }, () => {
            if (chrome.runtime.lastError) {
                console.warn('Não foi possível salvar configurações de tema no sync storage, tentando local storage');

                // Tentar salvar no local storage como fallback
                chrome.storage.local.set({ themeSettings }, () => {
                    if (chrome.runtime.lastError) {
                        reject(new Error('Não foi possível salvar configurações de tema: ' + chrome.runtime.lastError.message));
                    } else {
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    });
}

// Função para salvar um favorito
function handleSaveBookmark(data, sendResponse) {
    if (!data || !data.url) {
        sendResponse({ success: false, error: "Dados de favorito inválidos" });
        return;
    }
    
    try {
        // Verificar se a URL é válida
        new URL(data.url);
        
        // Criar favorito na pasta "Outros favoritos" (geralmente é a pasta padrão)
        chrome.bookmarks.create({
            title: data.title || data.url,
            url: data.url
        }, (bookmark) => {
            if (chrome.runtime.lastError) {
                console.error("Erro ao salvar favorito:", chrome.runtime.lastError);
                sendResponse({ success: false, error: chrome.runtime.lastError.message });
            } else {
                console.info("Favorito salvo com sucesso:", bookmark);
                sendResponse({ success: true, bookmark });
            }
        });
    } catch (error) {
        console.error("URL inválida:", data.url);
        sendResponse({ success: false, error: "URL inválida" });
    }
}

// Função para abrir o popup da extensão
function handleOpenPopup() {
    chrome.action.openPopup();
}

// Função para abrir a extensão em uma nova aba
function openExtensionInTab() {
    // Obter URL da página do popup
    const popupURL = chrome.runtime.getURL("popup/popup.html");
    
    // Verificar se já existe uma aba com a extensão aberta
    chrome.tabs.query({url: popupURL}, (tabs) => {
        if (tabs.length > 0) {
            // Se já existe uma aba, focar nela
            chrome.tabs.update(tabs[0].id, {active: true});
        } else {
            // Se não existe, criar uma nova aba
            chrome.tabs.create({url: popupURL});
        }
    });
}