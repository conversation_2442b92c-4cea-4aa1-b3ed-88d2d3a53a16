/**
 * Debug para verificar ordem dos favoritos
 * 
 * Este arquivo contém funções para debugar problemas de ordenação
 * dos favoritos na extensão.
 */

window.BookmarkOrderDebug = window.BookmarkOrderDebug || {};

(function() {
    'use strict';

    /**
     * Testa a ordem dos favoritos de uma pasta específica
     * @param {string} folderId - ID da pasta para testar
     */
    function testBookmarkOrder(folderId) {
        console.log(`[OrderDebug] Testando ordem dos favoritos na pasta: ${folderId}`);
        
        chrome.bookmarks.getChildren(folderId, (children) => {
            if (chrome.runtime.lastError) {
                console.error('[OrderDebug] Erro ao obter favoritos:', chrome.runtime.lastError);
                return;
            }
            
            console.log(`[OrderDebug] Total de itens retornados: ${children.length}`);
            
            // Filtrar apenas favoritos (com URL)
            const bookmarks = children.filter(child => child.url);
            console.log(`[OrderDebug] Favoritos encontrados: ${bookmarks.length}`);
            
            // Mostrar ordem original da API
            console.log('[OrderDebug] Ordem original da API:');
            bookmarks.forEach((bookmark, index) => {
                console.log(`  ${index + 1}. [${bookmark.index}] ${bookmark.title} - ${bookmark.url}`);
            });
            
            // Mostrar ordem após ordenação
            const sortedBookmarks = [...bookmarks].sort((a, b) => (a.index || 0) - (b.index || 0));
            console.log('[OrderDebug] Ordem após ordenação por índice:');
            sortedBookmarks.forEach((bookmark, index) => {
                console.log(`  ${index + 1}. [${bookmark.index}] ${bookmark.title} - ${bookmark.url}`);
            });
            
            // Verificar se há diferença
            const isDifferent = bookmarks.some((bookmark, index) => 
                bookmark.id !== sortedBookmarks[index].id
            );
            
            if (isDifferent) {
                console.warn('[OrderDebug] ⚠️ PROBLEMA: A API retornou favoritos fora de ordem!');
                console.log('[OrderDebug] A ordenação manual é necessária.');
            } else {
                console.log('[OrderDebug] ✅ A API retornou favoritos na ordem correta.');
            }
        });
    }

    /**
     * Testa a ordem dos favoritos de todas as pastas selecionadas
     */
    function testSelectedFoldersOrder() {
        console.log('[OrderDebug] Testando ordem de todas as pastas selecionadas...');
        
        if (typeof selectedFolderIds === 'undefined') {
            console.error('[OrderDebug] selectedFolderIds não está disponível');
            return;
        }
        
        if (selectedFolderIds.size === 0) {
            console.log('[OrderDebug] Nenhuma pasta selecionada');
            return;
        }
        
        Array.from(selectedFolderIds).forEach(folderId => {
            testBookmarkOrder(folderId);
        });
    }

    /**
     * Compara a ordem dos favoritos na interface vs API
     */
    function compareInterfaceVsAPI() {
        console.log('[OrderDebug] Comparando ordem da interface vs API...');
        
        const bookmarkElements = document.querySelectorAll('.bookmark-item');
        console.log(`[OrderDebug] Elementos na interface: ${bookmarkElements.length}`);
        
        // Agrupar por pasta
        const elementsByFolder = new Map();
        bookmarkElements.forEach(element => {
            const folderId = element.dataset.folder;
            const bookmarkId = element.dataset.id;
            
            if (!elementsByFolder.has(folderId)) {
                elementsByFolder.set(folderId, []);
            }
            elementsByFolder.get(folderId).push(bookmarkId);
        });
        
        // Comparar cada pasta
        elementsByFolder.forEach((interfaceIds, folderId) => {
            console.log(`[OrderDebug] Comparando pasta ${folderId}:`);
            console.log(`[OrderDebug] IDs na interface: ${interfaceIds.join(', ')}`);
            
            chrome.bookmarks.getChildren(folderId, (children) => {
                if (chrome.runtime.lastError) {
                    console.error(`[OrderDebug] Erro ao obter favoritos da pasta ${folderId}:`, chrome.runtime.lastError);
                    return;
                }
                
                const apiBookmarks = children
                    .filter(child => child.url)
                    .sort((a, b) => (a.index || 0) - (b.index || 0));
                
                const apiIds = apiBookmarks.map(bookmark => bookmark.id);
                console.log(`[OrderDebug] IDs da API ordenados: ${apiIds.join(', ')}`);
                
                // Verificar se são iguais
                const isEqual = interfaceIds.length === apiIds.length &&
                    interfaceIds.every((id, index) => id === apiIds[index]);
                
                if (isEqual) {
                    console.log(`[OrderDebug] ✅ Pasta ${folderId}: Interface e API estão sincronizadas`);
                } else {
                    console.warn(`[OrderDebug] ⚠️ Pasta ${folderId}: Interface e API estão DESSINCRONIZADAS!`);
                    
                    // Mostrar diferenças
                    interfaceIds.forEach((id, index) => {
                        if (id !== apiIds[index]) {
                            console.log(`[OrderDebug]   Posição ${index + 1}: Interface=${id}, API=${apiIds[index] || 'undefined'}`);
                        }
                    });
                }
            });
        });
    }

    /**
     * Monitora mudanças na ordem dos favoritos
     */
    function monitorOrderChanges() {
        console.log('[OrderDebug] Iniciando monitoramento de mudanças de ordem...');
        
        // Observar mudanças no container de favoritos
        const bookmarksContainer = document.getElementById('bookmarksContainer');
        if (!bookmarksContainer) {
            console.error('[OrderDebug] Container de favoritos não encontrado');
            return;
        }
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    console.log('[OrderDebug] Mudança detectada no container de favoritos');
                    
                    // Aguardar um pouco para a renderização completar
                    setTimeout(() => {
                        compareInterfaceVsAPI();
                    }, 500);
                }
            });
        });
        
        observer.observe(bookmarksContainer, {
            childList: true,
            subtree: true
        });
        
        console.log('[OrderDebug] Monitoramento ativo');
        return observer;
    }

    /**
     * Para o monitoramento
     */
    function stopMonitoring() {
        if (window.orderMonitor) {
            window.orderMonitor.disconnect();
            window.orderMonitor = null;
            console.log('[OrderDebug] Monitoramento parado');
        }
    }

    // API pública
    window.BookmarkOrderDebug = {
        testBookmarkOrder,
        testSelectedFoldersOrder,
        compareInterfaceVsAPI,
        monitorOrderChanges,
        stopMonitoring
    };

    // Comandos globais para debug
    window.testOrder = (folderId) => {
        if (folderId) {
            testBookmarkOrder(folderId);
        } else {
            testSelectedFoldersOrder();
        }
    };
    
    window.compareOrder = () => compareInterfaceVsAPI();
    window.monitorOrder = () => {
        window.orderMonitor = monitorOrderChanges();
    };
    window.stopOrderMonitor = () => stopMonitoring();

    console.log('[OrderDebug] Sistema de debug de ordem carregado');
    console.log('[OrderDebug] Comandos disponíveis:');
    console.log('[OrderDebug]   testOrder(folderId) - Testa ordem de uma pasta específica');
    console.log('[OrderDebug]   testOrder() - Testa ordem de todas as pastas selecionadas');
    console.log('[OrderDebug]   compareOrder() - Compara interface vs API');
    console.log('[OrderDebug]   monitorOrder() - Inicia monitoramento');
    console.log('[OrderDebug]   stopOrderMonitor() - Para monitoramento');

})();
