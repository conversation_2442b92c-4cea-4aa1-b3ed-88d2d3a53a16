# Sistema de Arraste Múltiplo de Favoritos

## Visão Geral

Implementado sistema que permite arrastar múltiplos favoritos selecionados simultaneamente, mantendo a ordem relativa original entre eles durante o drop.

## Funcionalidades Implementadas

### 1. **Detecção de Arraste Múltiplo**

#### Condições para Ativar:
- **Múltiplos favoritos selecionados** (2 ou mais)
- **Drag iniciado em um favorito selecionado**
- **Sistema detecta automaticamente** e ativa modo múltiplo

#### Comportamento:
```javascript
// Verificar se há múltiplos favoritos selecionados
const selectedBookmarks = getSelectedBookmarkElements();
const targetIsSelected = selectedBookmarks.includes(target);

if (selectedBookmarks.length > 1 && targetIsSelected) {
  // Ativar modo de arraste múltiplo
  isMultipleDrag = true;
  draggedElements = [...selectedBookmarks];
}
```

### 2. **Feedback Visual Diferenciado**

#### Arraste Simples:
- Ghost element mostra o título do favorito
- Exemplo: `"Meu Site Favorito"`

#### Arraste Múltiplo:
- Ghost element mostra a quantidade
- Exemplo: `"5 favoritos"`
- Todos os elementos selecionados ficam com classe `.dragging`

### 3. **Preservação da Ordem Relativa**

#### Algoritmo de Ordenação:
```javascript
// Ordenar elementos pela posição original no DOM
const sortedElements = [...elements].sort((a, b) => {
  const aIndex = parseInt(a.dataset.index, 10) || 0;
  const bIndex = parseInt(b.dataset.index, 10) || 0;
  return aIndex - bIndex;
});
```

#### Resultado:
- Favoritos mantêm a **mesma ordem relativa** que tinham antes
- Se estavam em posições 2, 5, 7 → continuam nessa sequência
- Inseridos **sequencialmente** a partir do ponto de drop

### 4. **Movimentação Sequencial**

#### Processo:
1. **Determinar posição inicial** (onde foi feito o drop)
2. **Mover primeiro favorito** para a posição inicial
3. **Mover segundo favorito** para posição inicial + 1
4. **Continuar sequencialmente** até todos serem movidos
5. **Manter vizinhança** entre os favoritos movidos

#### Código:
```javascript
const moveNext = (index) => {
  if (index >= sortedIds.length) {
    // Todos movidos - atualizar UI
    return;
  }
  
  const bookmarkId = sortedIds[index];
  const moveProperties = { 
    parentId: newParentId,
    index: currentIndex // Posição sequencial
  };
  
  chrome.bookmarks.move(bookmarkId, moveProperties, () => {
    currentIndex++; // Próxima posição
    moveNext(index + 1); // Mover próximo
  });
};
```

## Variáveis de Controle

### Novas Variáveis Globais:
```javascript
let draggedElements = [];     // Array de elementos sendo arrastados
let isMultipleDrag = false;   // Flag para indicar arraste múltiplo
```

### Estados do Sistema:
- **`isMultipleDrag = false`**: Arraste simples (1 favorito)
- **`isMultipleDrag = true`**: Arraste múltiplo (2+ favoritos)

## Fluxo de Funcionamento

### 1. **Início do Drag (`handleBookmarkDragStart`)**
```
┌─ Usuário inicia drag em favorito
├─ Sistema verifica se há múltiplos selecionados
├─ Se SIM: Ativa modo múltiplo
│  ├─ Coleta todos os elementos selecionados
│  ├─ Aplica classe .dragging a todos
│  └─ Cria ghost "X favoritos"
└─ Se NÃO: Modo simples (comportamento original)
```

### 2. **Durante o Drag (`handleDragOver`)**
```
┌─ Indicadores visuais funcionam normalmente
├─ Drop indicators aparecem onde será inserido
└─ Sistema não diferencia entre simples/múltiplo
```

### 3. **Drop (`handleDrop`)**
```
┌─ Sistema detecta se é arraste múltiplo
├─ Se SIM: Chama moveMultipleBookmarks()
│  ├─ Ordena favoritos pela posição original
│  ├─ Move sequencialmente mantendo ordem
│  └─ Atualiza UI após todos serem movidos
└─ Se NÃO: Chama moveBookmark() (original)
```

### 4. **Fim do Drag (`handleBookmarkDragEnd`)**
```
┌─ Remove classe .dragging de todos os elementos
├─ Limpa variáveis de controle
├─ Reset do estado do sistema
└─ Remove indicadores visuais
```

## Cenários de Uso

### **Cenário 1: Reordenar Favoritos na Mesma Pasta**
1. Selecionar favoritos nas posições 2, 4, 6
2. Arrastar para antes do favorito na posição 8
3. **Resultado**: Favoritos ficam nas posições 8, 9, 10 (mantendo ordem 2→4→6)

### **Cenário 2: Mover para Outra Pasta**
1. Selecionar múltiplos favoritos
2. Arrastar para dentro de uma pasta
3. **Resultado**: Favoritos são inseridos no final da pasta, mantendo ordem relativa

### **Cenário 3: Inserir Entre Favoritos**
1. Selecionar favoritos A, C, E (nessa ordem original)
2. Arrastar para entre favoritos X e Y
3. **Resultado**: Sequência fica X → A → C → E → Y

## Vantagens do Sistema

### 1. **Preservação da Ordem**
- ✅ Favoritos mantêm ordem relativa original
- ✅ Não há embaralhamento ou reorganização indesejada
- ✅ Comportamento previsível e intuitivo

### 2. **Eficiência**
- ✅ Move múltiplos favoritos em uma operação
- ✅ Feedback visual claro durante o processo
- ✅ Atualização de UI otimizada

### 3. **Compatibilidade**
- ✅ Funciona com sistema de seleção existente
- ✅ Não quebra funcionalidade de arraste simples
- ✅ Integra-se com indicadores visuais

### 4. **Robustez**
- ✅ Tratamento de erros individual por favorito
- ✅ Continua processo mesmo se um favorito falhar
- ✅ Feedback de sucesso/erro apropriado

## Arquivos Modificados

### `popup/dragdrop.js`
**Novas funcionalidades:**
- Variáveis `draggedElements` e `isMultipleDrag`
- Lógica de detecção múltipla em `handleBookmarkDragStart`
- Função `moveMultipleBookmarks()` para movimentação sequencial
- Função `updateUIAfterMove()` para atualização de UI
- Limpeza de estado em `handleBookmarkDragEnd`

**Integrações:**
- Sistema funciona com `getSelectedBookmarkElements()` do `selection.js`
- Usa APIs existentes do Chrome (`chrome.bookmarks.move`)
- Mantém compatibilidade com sistema de feedback

## Resultado Final

### ✅ **Funcionalidades Implementadas:**
1. **Arraste múltiplo automático** - Detecta e ativa quando necessário
2. **Preservação de ordem** - Mantém sequência relativa original
3. **Feedback visual** - Ghost element mostra quantidade
4. **Movimentação sequencial** - Insere favoritos como vizinhos
5. **Compatibilidade total** - Não quebra funcionalidades existentes

### 🎯 **Casos de Uso Atendidos:**
- ✅ Reordenar múltiplos favoritos na mesma pasta
- ✅ Mover múltiplos favoritos para outra pasta
- ✅ Inserir múltiplos favoritos entre outros favoritos
- ✅ Manter ordem relativa em todas as operações

O sistema de arraste múltiplo está **totalmente funcional** e oferece uma experiência de usuário intuitiva e eficiente para gerenciar múltiplos favoritos simultaneamente!
