# Sistema de Drag and Drop - Bookmark Folder Merger

## Visão Geral

O sistema de drag and drop foi implementado para permitir que os usuários reorganizem favoritos e pastas de forma intuitiva através de arrastar e soltar elementos entre as duas colunas do popup.

## Funcionalidades Implementadas

### 1. <PERSON>ag and Drop de Favoritos

#### Entre Favoritos na Mesma Coluna
- **Funcionalidade**: Arrastar um favorito e soltar antes ou depois de outro favorito
- **Resultado**: O favorito é reposicionado na mesma pasta, alterando sua ordem
- **Indicadores Visuais**: 
  - Linha azul acima do alvo = inserir antes
  - Linha azul abaixo do alvo = inserir depois

#### Entre Favoritos de Pastas Diferentes
- **Funcionalidade**: Arrastar um favorito de uma pasta e soltar em favoritos de outra pasta
- **Resultado**: O favorito é movido para a pasta de destino na posição especificada
- **Requisito**: <PERSON><PERSON> as pastas devem estar selecionadas e visíveis

#### De Favorito para Pasta
- **Funcionalidade**: Arrastar um favorito e soltar dentro de uma pasta na coluna de pastas
- **Resultado**: O favorito é movido para dentro da pasta de destino
- **Indicador Visual**: Borda azul tracejada ao redor da pasta = inserir dentro

### 2. Drag and Drop de Pastas

#### Status Atual
- **Implementação**: Parcial (estrutura básica criada)
- **Funcionalidade**: Detecta drag de pastas mas ainda não executa movimentação
- **Próximos Passos**: Implementação completa da movimentação hierárquica de pastas

## Como Usar

### Para Favoritos:

1. **Selecione as pastas** que contêm os favoritos que você quer reorganizar
2. **Clique e arraste** um favorito da coluna direita
3. **Solte o favorito**:
   - **Sobre outro favorito**: para reposicionar na mesma pasta ou mover para outra pasta
   - **Sobre uma pasta**: para mover o favorito para dentro dessa pasta

### Indicadores Visuais Durante o Drag:

- **Elemento sendo arrastado**: Fica semi-transparente e ligeiramente rotacionado
- **Linha azul acima**: Indica que o item será inserido antes do alvo
- **Linha azul abaixo**: Indica que o item será inserido depois do alvo  
- **Borda azul tracejada**: Indica que o item será inserido dentro da pasta

## Arquivos Modificados

### Novos Arquivos:
- `popup/dragdrop.js` - Sistema principal de drag and drop

### Arquivos Modificados:
- `popup/popup.html` - Adicionado script dragdrop.js
- `popup/popup.js` - Inicialização do sistema de drag and drop
- `popup/render.js` - Adicionado atributo draggable="true" aos favoritos
- `popup/events.js` - Adicionado atributo draggable="true" às pastas

## Estrutura Técnica

### Variáveis Globais:
- `draggedElement` - Elemento sendo arrastado
- `draggedElementType` - Tipo: 'bookmark' ou 'folder'
- `dropTarget` - Elemento alvo do drop
- `dropPosition` - Posição: 'before', 'after', 'inside'

### Funções Principais:
- `initDragAndDrop()` - Inicializa o sistema
- `setupBookmarkDragEvents()` - Configura eventos para favoritos
- `setupFolderDragEvents()` - Configura eventos para pastas
- `handleDragOver()` - Gerencia indicadores visuais durante drag
- `handleDrop()` - Executa a movimentação
- `moveBookmark()` - Move favorito via API do Chrome

### Estilos CSS Dinâmicos:
- `.dragging` - Estilo do elemento sendo arrastado
- `.drop-before` - Indicador de inserção antes
- `.drop-after` - Indicador de inserção depois
- `.drop-inside` - Indicador de inserção dentro

## Integração com APIs do Chrome

O sistema utiliza a API `chrome.bookmarks.move()` para:
- Mover favoritos entre pastas
- Reposicionar favoritos dentro da mesma pasta
- Manter a sincronização com a árvore de favoritos do navegador

## Feedback Visual

- **Mensagens de sucesso**: "Favorito movido com sucesso"
- **Mensagens de erro**: Exibidas quando há problemas na movimentação
- **Atualização automática**: A interface é atualizada automaticamente após cada movimentação

## Limitações Atuais

1. **Movimentação de pastas**: Ainda em desenvolvimento
2. **Drag entre janelas**: Funciona apenas dentro do popup
3. **Múltipla seleção**: Não suporta arrastar múltiplos itens simultaneamente

## Próximas Melhorias

1. Implementar movimentação completa de pastas
2. Suporte a múltipla seleção para drag
3. Animações mais suaves
4. Suporte a drag entre diferentes instâncias da extensão
