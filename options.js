// Configurações padrão
const defaultSettings = {
    fontSize: 'medium',
    fontWeight: 'normal',  // 'normal' (400) ou 'semibold' (500)
    confirmDelete: true,
    confirmMerge: true,
    favoriteFolders: []
};

// Elementos da interface
let elements = {};

// Inicializar a página
document.addEventListener('DOMContentLoaded', async () => {
    // Inicializar elementos da interface
    initializeElements();
    
    // Carregar configurações
    await loadSettings();
    
    // Configurar event listeners
    setupEventListeners();
});

// Inicializar todos os elementos da interface
function initializeElements() {
    elements = {
        fontSize: document.getElementById('fontSize'),
        confirmDelete: document.getElementById('confirmDelete'),
        confirmMerge: document.getElementById('confirmMerge'),
        exportBtn: document.getElementById('exportBtn'),
        importBtn: document.getElementById('importBtn'),
        importFile: document.getElementById('importFile'),
        resetBtn: document.getElementById('resetBtn'),
        saveBtn: document.getElementById('saveBtn')
    };
}

// Carregar configurações salvas
async function loadSettings() {
    try {
        const settings = await getStoredSettings();

        // Verificar se cada elemento existe antes de aplicar as configurações
        if (elements.fontSize) {
            elements.fontSize.value = settings.fontSize || defaultSettings.fontSize;
        }
        
        if (elements.confirmDelete) {
            elements.confirmDelete.checked = settings.confirmDelete !== undefined ? 
                settings.confirmDelete : defaultSettings.confirmDelete;
        }
        
        if (elements.confirmMerge) {
            elements.confirmMerge.checked = settings.confirmMerge !== undefined ? 
                settings.confirmMerge : defaultSettings.confirmMerge;
        }
    } catch (error) {
        console.error('Erro ao carregar configurações:', error);
        showNotification('Erro ao carregar configurações', 'error');
    }
}

// Obter configurações armazenadas
async function getStoredSettings() {
    return new Promise((resolve) => {
        // Primeiro tentar obter as configurações do storage.sync
        chrome.storage.sync.get('settings', (result) => {
            if (chrome.runtime.lastError) {
                console.warn('Erro ao acessar storage.sync:', chrome.runtime.lastError);
                // Se falhar, tentar o storage.local como fallback
                chrome.storage.local.get('settings', (localResult) => {
                    resolve(localResult.settings || defaultSettings);
                });
            } else {
                resolve(result.settings || defaultSettings);
            }
        });
    });
}

// Configurar event listeners
function setupEventListeners() {
    // Verificar se cada elemento existe antes de adicionar listeners

    // Salvar configurações
    if (elements.saveBtn) {
        elements.saveBtn.addEventListener('click', saveSettings);
    }

    // Restaurar configurações padrão
    if (elements.resetBtn) {
        elements.resetBtn.addEventListener('click', resetSettings);
    }

    // Exportar configurações
    if (elements.exportBtn) {
        elements.exportBtn.addEventListener('click', exportSettings);
    }

    // Importar configurações
    if (elements.importBtn && elements.importFile) {
        elements.importBtn.addEventListener('click', () => {
            elements.importFile.click();
        });
        elements.importFile.addEventListener('change', importSettings);
    }

    // Botão de Reset Completo
    const hardResetBtn = document.getElementById('hardResetBtn');
    if (hardResetBtn) {
        hardResetBtn.addEventListener('click', hardReset);
    }
}

// Salvar configurações
async function saveSettings() {
    try {
        // Verificar se os elementos obrigatórios existem
        if (!elements.fontSize || !elements.confirmDelete || !elements.confirmMerge) {
            throw new Error('Elementos da interface não encontrados');
        }

        const settings = {
            fontSize: elements.fontSize.value,
            confirmDelete: elements.confirmDelete.checked,
            confirmMerge: elements.confirmMerge.checked,
            favoriteFolders: await getFavoriteFoldersData()
        };

        // Tentar salvar no storage.sync, com fallback para storage.local
        try {
            await new Promise((resolve, reject) => {
                chrome.storage.sync.set({ settings }, () => {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve();
                    }
                });
            });
        } catch (syncError) {
            console.warn('Falha ao salvar no storage.sync, usando storage.local:', syncError);
            await new Promise((resolve) => {
                chrome.storage.local.set({ settings }, resolve);
            });
        }

        showNotification('Configurações salvas com sucesso!', 'success');
        
        // Tentar atualizar popups abertos
        try {
            // Usar a API do Chrome para obter todas as janelas popup da extensão
            chrome.runtime.sendMessage({ action: 'configUpdated', settings }, (response) => {
                if (chrome.runtime.lastError) {
                    console.warn('Não foi possível comunicar com popups abertos:', chrome.runtime.lastError);
                } else if (response && response.success) {
                    console.log('Popups notificados com sucesso sobre a atualização das configurações');
                }
            });
        } catch (error) {
            console.warn('Erro ao tentar notificar popups abertos:', error);
        }
    } catch (error) {
        console.error('Erro ao salvar configurações:', error);
        showNotification('Erro ao salvar configurações: ' + error.message, 'error');
    }
}

// Restaurar configurações padrão
async function resetSettings() {
    if (confirm('Tem certeza que deseja restaurar todas as configurações para os valores padrão?')) {
        try {
            // Verificar se cada elemento existe antes de restaurar valores
            if (elements.fontSize) {
                elements.fontSize.value = defaultSettings.fontSize;
            }

        

            if (elements.confirmDelete) {
                elements.confirmDelete.checked = defaultSettings.confirmDelete;
            }

            if (elements.confirmMerge) {
                elements.confirmMerge.checked = defaultSettings.confirmMerge;
            }

            // Salvar as configurações resetadas
            await saveSettings();

            showNotification('Configurações restauradas para os valores padrão', 'info');
        } catch (error) {
            console.error('Erro ao resetar configurações:', error);
            showNotification('Erro ao restaurar configurações', 'error');
        }
    }
}

// Limpar todo o storage
async function hardReset() {
    if (confirm('ATENÇÃO: Isso irá apagar TODAS as configurações da extensão (gerais e de tema) de forma irreversível. Deseja continuar?')) {
        try {
            // Limpar todo o storage
            await Promise.all([
                new Promise(resolve => chrome.storage.sync.clear(resolve)),
                new Promise(resolve => chrome.storage.local.clear(resolve))
            ]);

            // Reinicializar com configurações padrão
            await Promise.all([
                new Promise(resolve => chrome.storage.sync.set({ settings: defaultSettings }, resolve)),
                new Promise(resolve => chrome.storage.sync.set({ themeSettings: defaultThemeSettings }, resolve))
            ]);

            // Notificar o popup sobre as mudanças
            try {
                chrome.runtime.sendMessage({
                    action: 'configUpdated',
                    settings: defaultSettings
                });
                chrome.runtime.sendMessage({
                    action: 'themeUpdated',
                    themeSettings: defaultThemeSettings
                });
                console.log('Mensagens de reset enviadas para o popup');
            } catch (messageError) {
                console.log('Popup não está aberto ou erro ao enviar mensagem:', messageError);
            }

            // Atualizar configurações locais
            currentThemeSettings = JSON.parse(JSON.stringify(defaultThemeSettings));

            showNotification('Todas as configurações foram resetadas para os valores padrão.', 'success');

            // Recarregar a página após um delay menor
            setTimeout(() => {
                location.reload();
            }, 1000);

        } catch (error) {
            showNotification('Ocorreu um erro ao resetar as configurações.', 'error');
            console.error('Erro no reset completo:', error);
        }
    }
}

// Exportar configurações
async function exportSettings() {
    try {
        const settings = await getStoredSettings();

        const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'bookmark-folder-merger-settings.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('Configurações exportadas com sucesso!', 'success');
    } catch (error) {
        console.error('Erro ao exportar configurações:', error);
        showNotification('Erro ao exportar configurações', 'error');
    }
}

// Importar configurações
async function importSettings(event) {
    try {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = async (e) => {
            try {
                const settings = JSON.parse(e.target.result);
                
                // Validar configurações importadas
                if (!settings || typeof settings !== 'object') {
                    throw new Error('Arquivo de configurações inválido');
                }
                
                // Aplicar configurações para cada elemento que existe
                if (elements.fontSize) {
                    elements.fontSize.value = settings.fontSize || defaultSettings.fontSize;
                }
                
       
                
                if (elements.confirmDelete) {
                    elements.confirmDelete.checked = settings.confirmDelete !== undefined ? 
                        settings.confirmDelete : defaultSettings.confirmDelete;
                }
                
                if (elements.confirmMerge) {
                    elements.confirmMerge.checked = settings.confirmMerge !== undefined ? 
                        settings.confirmMerge : defaultSettings.confirmMerge;
                }
                
                // Limpar o input de arquivo
                if (elements.importFile) {
                    elements.importFile.value = '';
                }
                
                showNotification('Configurações importadas com sucesso!', 'success');
            } catch (error) {
                console.error('Erro ao processar arquivo de configurações:', error);
                showNotification('Arquivo de configurações inválido', 'error');
            }
        };
        reader.readAsText(file);
    } catch (error) {
        console.error('Erro ao importar configurações:', error);
        showNotification('Erro ao importar configurações', 'error');
    }
}

// Obter dados das pastas favoritas
async function getFavoriteFoldersData() {
    try {
        const settings = await getStoredSettings();
        return settings.favoriteFolders || [];
    } catch (error) {
        console.error('Erro ao obter dados das pastas favoritas:', error);
        return [];
    }
}

// Mostrar notificação
function showNotification(message, type = 'info') {
    // Verificar se já existe uma notificação
    let notification = document.querySelector('.notification');
    
    if (notification) {
        // Remover notificação existente
        notification.remove();
    }
    
    // Criar nova notificação
    notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Estilizar notificação
    Object.assign(notification.style, {
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '4px',
        color: '#fff',
        fontWeight: '500',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
        zIndex: '9999',
        transition: 'opacity 0.3s ease',
        opacity: '0'
    });
    
    // Definir cor de fundo com base no tipo
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#28a745';
            break;
        case 'error':
            notification.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            notification.style.backgroundColor = '#ffc107';
            notification.style.color = '#212529';
            break;
        default:
            notification.style.backgroundColor = '#17a2b8';
    }
    
    // Adicionar ao documento
    document.body.appendChild(notification);
    
    // Animar entrada
    setTimeout(() => {
        notification.style.opacity = '1';
    }, 10);
    
    // Remover após 3 segundos
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// =================================================================================
// LÓGICA DE PERSONALIZAÇÃO DE TEMA
// =================================================================================

const defaultThemeSettings = {
    light: {
        name: 'Tema Claro',
        states: {
            normal: {
                name: 'Normal',
                background: { name: 'Fundo', enabled: true, color: '#ffffff', opacity: 1 },
                border: { name: 'Borda', enabled: true, color: '#dddddd', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#000000', opacity: 0.1, size: 2 }
            },
            selected: {
                name: 'Selecionado',
                background: { name: 'Fundo', enabled: true, color: '#efeef6', opacity: 1 },
                border: { name: 'Borda', enabled: false, color: '#000000', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#000000', opacity: 0.1, size: 2 }
            },
            hover: {
                name: 'Hover',
                background: { name: 'Fundo', enabled: true, color: '#e0dfe7', opacity: 1 },
                border: { name: 'Borda', enabled: false, color: '#000000', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#000000', opacity: 0.1, size: 2 },
                underline: { name: 'Underline', enabled: true, thickness: 1, opacity: 0.55, color: '#333333' }
            }
        }
    },
    dark: {
        name: 'Tema Escuro',
        states: {
            normal: {
                name: 'Normal',
                background: { name: 'Fundo', enabled: true, color: '#2a2a2e', opacity: 1 },
                border: { name: 'Borda', enabled: true, color: '#444444', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#000000', opacity: 0.2, size: 2 }
            },
            selected: {
                name: 'Selecionado',
                background: { name: 'Fundo', enabled: true, color: '#4a4a50', opacity: 1 },
                border: { name: 'Borda', enabled: false, color: '#ffffff', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#ffffff', opacity: 0.1, size: 2 },
            },
            hover: {
                name: 'Hover',
                background: { name: 'Fundo', enabled: true, color: '#686576', opacity: 1 },
                border: { name: 'Borda', enabled: false, color: '#ffffff', opacity: 1, thickness: 1 },
                shadow: { name: 'Sombra', enabled: false, color: '#ffffff', opacity: 0.1, size: 2 },
                underline: { name: 'Underline', enabled: true, thickness: 1, opacity: 0.55, color: '#e0e0e0' }
            }
        }
    }
};

let currentThemeSettings = JSON.parse(JSON.stringify(defaultThemeSettings));

function createThemeCustomizationControls() {
    const themeOrder = ['light', 'dark']; // Garante a ordem de renderização
    const stateOrder = ['normal', 'selected', 'hover']; // Ordem correta dos estados

    themeOrder.forEach(themeKey => {
        const container = document.getElementById(`theme-customization-${themeKey}`);
        if (!container) return;
        container.innerHTML = '';

        const theme = currentThemeSettings[themeKey];
        if (!theme) return;

        const themeSection = document.createElement('div');
        themeSection.className = 'theme-section';

        stateOrder.forEach(stateKey => {
            const state = theme.states[stateKey];
            if (!state) return;

            const stateSection = document.createElement('div');
            stateSection.className = 'state-section collapsed'; // Começa recolhido

            const stateTitle = document.createElement('div');
            stateTitle.className = 'state-title';
            stateTitle.textContent = state.name;
            stateTitle.addEventListener('click', () => {
                stateSection.classList.toggle('collapsed');
            });

            const controlsWrapper = document.createElement('div');
            controlsWrapper.className = 'state-controls-wrapper';

            for (const propKey in state) {
                if (propKey === 'name') continue;
                const prop = state[propKey];
                const controlHtml = createPropertyControl(themeKey, stateKey, propKey, prop);
                controlsWrapper.innerHTML += controlHtml;
            }

            stateSection.appendChild(stateTitle);
            stateSection.appendChild(controlsWrapper);
            themeSection.appendChild(stateSection);
        });
        container.appendChild(themeSection);
    });

    addThemeEventListeners();
}

function createPropertyControl(themeKey, stateKey, propKey, prop) {
    const idPrefix = `theme-${themeKey}-${stateKey}-${propKey}`;
    
    let controls = '';
    const hasColor = prop.hasOwnProperty('color');

    // Cria um container para o input de cor e o de texto
    const createColorInputs = () => {
        if (!hasColor) return '';
        return `
            <div class="color-input-container">
                <input type="color" id="${idPrefix}-color" value="${prop.color}">
                <input type="text" id="${idPrefix}-hex" value="${prop.color}" class="hex-color-input" maxlength="7">
            </div>
        `;
    };

    if (propKey === 'underline') {
        controls = `
            <label for="${idPrefix}-thickness">Espessura</label>
            <input type="range" id="${idPrefix}-thickness" min="1" max="5" step="1" value="${prop.thickness}">
            <label for="${idPrefix}-opacity">Opacidade</label>
            <input type="range" id="${idPrefix}-opacity" min="0.25" max="1" step="0.15" value="${prop.opacity}">
        `;
    } else if (propKey === 'border') {
        controls = `
            <label for="${idPrefix}-color">Cor</label>
            ${createColorInputs()}
            <label for="${idPrefix}-opacity">Opacidade</label>
            <input type="range" id="${idPrefix}-opacity" min="0.25" max="1" step="0.15" value="${prop.opacity}">
            <label for="${idPrefix}-thickness">Espessura</label>
            <input type="range" id="${idPrefix}-thickness" min="1" max="5" step="1" value="${prop.thickness}">
        `;
    } else if (propKey === 'shadow') {
        controls = `
            <label for="${idPrefix}-color">Cor</label>
            ${createColorInputs()}
            <label for="${idPrefix}-opacity">Opacidade</label>
            <input type="range" id="${idPrefix}-opacity" min="0.25" max="1" step="0.15" value="${prop.opacity}">
            <label for="${idPrefix}-size">Tamanho</label>
            <input type="range" id="${idPrefix}-size" min="1" max="5" step="1" value="${prop.size}">
        `;
    } else { // background
        controls = `
            <label for="${idPrefix}-color">Cor</label>
            ${createColorInputs()}
            <label for="${idPrefix}-opacity">Opacidade</label>
            <input type="range" id="${idPrefix}-opacity" min="0.25" max="1" step="0.15" value="${prop.opacity}">
        `;
    }

    return `
        <div class="style-property-group" id="${idPrefix}-group">
            <div class="style-property-header">
                <input type="checkbox" id="${idPrefix}-enabled" ${prop.enabled ? 'checked' : ''}>
                <label for="${idPrefix}-enabled">${prop.name}</label>
            </div>
            <div class="style-controls" style="${!prop.enabled ? 'display: none;' : ''}">
                ${controls}
            </div>
        </div>
    `;
}

function addThemeEventListeners() {
    for (const themeKey in currentThemeSettings) {
        for (const stateKey in currentThemeSettings[themeKey].states) {
            for (const propKey in currentThemeSettings[themeKey].states[stateKey]) {
                if (propKey === 'name') continue;

                const idPrefix = `theme-${themeKey}-${stateKey}-${propKey}`;
                const enabledCheckbox = document.getElementById(`${idPrefix}-enabled`);
                
                enabledCheckbox.addEventListener('change', (e) => {
                    currentThemeSettings[themeKey].states[stateKey][propKey].enabled = e.target.checked;
                    document.querySelector(`#${idPrefix}-group .style-controls`).style.display = e.target.checked ? 'grid' : 'none';
                    updatePreviewStyles();
                });

                // Listeners para cor e opacidade (comuns a vários)
                const colorInput = document.getElementById(`${idPrefix}-color`);
                const hexInput = document.getElementById(`${idPrefix}-hex`);

                if(colorInput && hexInput) {
                    // Sincroniza o input de cor com o de texto
                    colorInput.addEventListener('input', (e) => {
                        const newColor = e.target.value;
                        hexInput.value = newColor;
                        currentThemeSettings[themeKey].states[stateKey][propKey].color = newColor;
                        updatePreviewStyles();
                    });

                    // Sincroniza o input de texto com o de cor
                    hexInput.addEventListener('input', (e) => {
                        let newColor = e.target.value;
                        // Validação simples de formato hexadecimal
                        if (/^#([0-9A-F]{3}){1,2}$/i.test(newColor)) {
                            colorInput.value = newColor;
                            currentThemeSettings[themeKey].states[stateKey][propKey].color = newColor;
                            updatePreviewStyles();
                        }
                    });
                }

                const opacityInput = document.getElementById(`${idPrefix}-opacity`);
                if (opacityInput) {
                    opacityInput.addEventListener('input', (e) => {
                        currentThemeSettings[themeKey].states[stateKey][propKey].opacity = parseFloat(e.target.value);
                        updatePreviewStyles();
                    });
                }
                
                // Listeners específicos
                if (propKey === 'underline' || propKey === 'border') {
                    const thicknessInput = document.getElementById(`${idPrefix}-thickness`);
                    thicknessInput.addEventListener('input', (e) => {
                        currentThemeSettings[themeKey].states[stateKey][propKey].thickness = parseInt(e.target.value, 10);
                        updatePreviewStyles();
                    });
                }

                if (propKey === 'shadow') {
                    const sizeInput = document.getElementById(`${idPrefix}-size`);
                    sizeInput.addEventListener('input', (e) => {
                        currentThemeSettings[themeKey].states[stateKey][propKey].size = parseInt(e.target.value, 10);
                        updatePreviewStyles();
                    });
                }
            }
        }
    }
}

function hexToRgba(hex, opacity) {
    let r = 0, g = 0, b = 0;
    if (hex.length == 4) {
        r = "0x" + hex[1] + hex[1];
        g = "0x" + hex[2] + hex[2];
        b = "0x" + hex[3] + hex[3];
    } else if (hex.length == 7) {
        r = "0x" + hex[1] + hex[2];
        g = "0x" + hex[3] + hex[4];
        b = "0x" + hex[5] + hex[6];
    }
    return `rgba(${+r},${+g},${+b},${opacity})`;
}

function generateAllPreviewCss() {
    let css = '<style id="dynamic-preview-styles">\n';

    for (const themeKey of ['light', 'dark']) {
        const theme = currentThemeSettings[themeKey];
        if (!theme) continue;
        
        const suffix = `-${themeKey}`;

        // Normal State
        const normal = theme.states.normal;
        let normalStyles = '';
        if (normal.background.enabled) normalStyles += `background-color: ${hexToRgba(normal.background.color, normal.background.opacity)};\n`;
        if (normal.border.enabled) normalStyles += `border-bottom: ${normal.border.thickness}px solid ${hexToRgba(normal.border.color, normal.border.opacity)};\n`;
        if (normal.shadow.enabled) normalStyles += `box-shadow: 0 ${normal.shadow.size}px ${normal.shadow.size * 2}px ${hexToRgba(normal.shadow.color, normal.shadow.opacity)};\n`;
        css += `#preview-normal${suffix} { ${normalStyles} }\n`;

        // Selected State
        const selected = theme.states.selected;
        let selectedStyles = '';
        if (selected.background.enabled) selectedStyles += `background-color: ${hexToRgba(selected.background.color, selected.background.opacity)};\n`;
        if (selected.border.enabled) selectedStyles += `border-bottom: ${selected.border.thickness}px solid ${hexToRgba(selected.border.color, selected.border.opacity)};\n`;
        if (selected.shadow.enabled) selectedStyles += `box-shadow: 0 ${selected.shadow.size}px ${selected.shadow.size * 2}px ${hexToRgba(selected.shadow.color, selected.shadow.opacity)};\n`;
        css += `#preview-selected${suffix} { ${selectedStyles} }\n`;
        
        // Hover State
        const hover = theme.states.hover;
        let hoverStyles = '';
        if (hover.background.enabled) hoverStyles += `background-color: ${hexToRgba(hover.background.color, hover.background.opacity)};\n`;
        if (hover.border.enabled) hoverStyles += `border-bottom: ${hover.border.thickness}px solid ${hexToRgba(hover.border.color, hover.border.opacity)};\n`;
        if (hover.shadow.enabled) hoverStyles += `box-shadow: 0 ${hover.shadow.size}px ${hover.shadow.size * 2}px ${hexToRgba(hover.shadow.color, hover.shadow.opacity)};\n`;
        
        // A lógica do sublinhado foi movida para fora para evitar duplicação
        css += `#preview-hover${suffix} { ${hoverStyles} }\n`;

        if (hover.underline.enabled) {
            // Usar a cor configurada para o sublinhado
            let underlineStyles = '';
            underlineStyles += `text-decoration: underline !important;\n`;
            underlineStyles += `text-decoration-thickness: ${hover.underline.thickness}px !important;\n`;
            underlineStyles += `text-decoration-color: ${hexToRgba(hover.underline.color, hover.underline.opacity)} !important;\n`;
            css += `#preview-hover${suffix} { ${underlineStyles} }\n`;
        } else {
             css += `#preview-hover${suffix} { text-decoration: none !important; }\n`;
        }
    }

    css += '</style>';
    return css;
}

function updatePreviewStyles() {
    const existingStyleTag = document.getElementById('dynamic-preview-styles');
    if (existingStyleTag) {
        existingStyleTag.remove();
    }
    const newCss = generateAllPreviewCss();
    document.head.insertAdjacentHTML('beforeend', newCss);
}

// Sobrescrever as funções de carregar e salvar para incluir as configurações do tema

const originalLoadSettings = loadSettings;
loadSettings = async function() {
    await originalLoadSettings(); // Carrega as configurações gerais primeiro
    try {
        const result = await new Promise(resolve => chrome.storage.sync.get('themeSettings', resolve));
        if (result.themeSettings) {
            currentThemeSettings = result.themeSettings;
        }
    } catch (error) {
        console.error("Erro ao carregar configurações de tema:", error);
    }
    createThemeCustomizationControls();
    updatePreviewStyles();
};

const originalSaveSettings = saveSettings;
saveSettings = async function() {
    await originalSaveSettings(); // Salva as configurações gerais primeiro
    try {
        await new Promise((resolve, reject) => {
            chrome.storage.sync.set({ themeSettings: currentThemeSettings }, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                    // Não mostra notificação aqui para evitar duplicidade
                }
            });
        });

        // Enviar mensagem para o popup aplicar os novos temas
        try {
            chrome.runtime.sendMessage({
                action: 'themeUpdated',
                themeSettings: currentThemeSettings
            });
            console.log('Mensagem de atualização de tema enviada para o popup');
        } catch (messageError) {
            console.log('Popup não está aberto ou erro ao enviar mensagem:', messageError);
        }

    } catch (error) {
        console.error('Erro ao salvar configurações de tema:', error);
        showNotification('Erro ao salvar as configurações do tema.', 'error');
    }
};