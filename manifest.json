{"manifest_version": 3, "name": "__MSG_extName__", "description": "__MSG_extDescription__", "version": "0.7", "default_locale": "pt_BR", "author": "Babaronga", "permissions": ["activeTab", "bookmarks", "storage", "tabs", "scripting", "contextMenus", "history", "favicon"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "options_page": "options.html", "options_ui": {"page": "options.html", "open_in_tab": true}, "content_scripts": [{"matches": ["<all_urls>"], "css": ["main.css"], "js": ["content.js"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; frame-ancestors 'none';"}, "action": {"default_popup": "popup/popup.html", "default_icon": {"64": "img/icons/bfm64.png", "128": "img/icons/bfm128.png", "256": "img/icons/bfm256.png"}}, "icons": {"64": "img/icons/bfm64.png", "128": "img/icons/bfm128.png", "256": "img/icons/bfm256.png"}, "commands": {"inject-script": {"suggested_key": {"default": "Ctrl+Shift+Y"}, "description": "Inject a script on the page"}}}