/*
  ==============================================
  ÍNDICE DE ESTILOS - BOOKMARK FOLDER MERGER
  ==============================================

  1. VA<PERSON>ÁVEIS E RESET
  2. SCROLLBARS  
  3. LAYOUT PRINCIPAL
  4. CABEÇALHOS E COLUNAS
  5. SEPARADOR
  6. TIPOGRAFIA
  7. FORMULÁRIOS E CONTROLES
  8. ITENS DE BOOKMARK
  9. PASTAS E OPÇÕES
  10. AÇÕES E BOTÕES 
  11. DROPDOWNS E MENUS
  12. CONTADORES E INDICADORES
  13. RODAPÉ
  ==============================================
*/

/* ==============================================
   1. VA<PERSON><PERSON>VEIS E RESET
   ============================================== */
:root {
  --yellow-bg: #fbe5aa;
  --yellow-accent: #e5b122;
  --text-color: #333;
  --light-overlay: rgba(255, 255, 255, 0.832);
  --hover-overlay: rgba(0, 0, 0, 0.05);
  --blue: #1e90ff;
  --accent: #0078D4;
  --accent-hover: #005A9E;
  --checkbox-size: 2em;
  --selected-bg: #ffe699;
  --border-color: #e0e0e0;
  --header-height: 90px;
  --footer-height: 60px;
  --indent-size: 10px;
  --min-column-width: 150px;
  --max-column-width: auto;
  --max-overlap: 150px;
  --separator-min-left: 100px;
  --primary: #0078D4;
  --primary-hover: #005A9E;
  --button-height: 38px;
  --item-height-normal: 28px; /* Altura padrão para itens (folder e bookmark) */
  --item-height-compact: 22px; /* Altura compacta para itens */
  --font-weight-normal: 400;  /* Peso da fonte normal */
  --font-weight-semibold: 500; /* Peso da fonte semi-negrito */
  
  /* Variáveis para o tema claro (padrão) */
  --bg-color: #e6e6e6;
  --content-bg: white;
  --item-bg: white;
  --item-hover-bg: #f0f0f0;
  --item-selected-bg: #ffe699;
  --scrollbar-thumb: rgba(0, 0, 0, 0.15);
  --scrollbar-thumb-hover: rgba(0, 0, 0, 0.3);
  --dropdown-bg: white;
  --dropdown-border: #ccc;
  --active-option-bg: #f0f5ff;
  --folder-icon-color: #e5b122;
  --folder-icon-bg: rgba(229, 177, 34, 0.15);
  --folder-nest-line: #e5e5e5;
  --input-bg: white;
  --input-text: #000000;
  --toast-bg: #444;
  --toast-text: #fff;
  --folder-title-color: #333;
  --folder-selected-text: #333;
  --primary-bg: #fff;
  --secondary-bg: #f7f7f7;
}

/* Variáveis para o tema escuro */
body.dark-theme {
  --bg-color: #1e1e1e;
  --content-bg: #2d2d2d;
  --text-color: #e0e0e0;
  --border-color: #444;
  --item-bg: #333333;
  --item-hover-bg: #3a3a3a;
  --item-selected-bg: #5a5000;
  --scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --scrollbar-thumb-hover: rgba(255, 255, 255, 0.4);
  --dropdown-bg: #333333;
  --dropdown-border: #555;
  --active-option-bg: #3a3d45;
  --folder-icon-color: #ffcc33;
  --folder-icon-bg: rgba(255, 204, 51, 0.3);
  --folder-nest-line: #666;
  --input-bg: #3a3a3a;
  --input-text: #e0e0e0;
  --toast-bg: #555;
  --toast-text: #fff;
  --folder-title-color: #e0e0e0;
  --folder-selected-text: #ffffff;
  --folders-column-bg: #252525;
  --primary-bg: #1e1e1e;
}

/* Aplicar fonte compacta quando a opção for escolhida */
body.font-size-small {
  --item-height-normal: var(--item-height-compact);
  font-size: 0.95em;
}

/* Aplicar o peso da fonte quando a opção semibold for escolhida */
body.font-weight-semibold .folder-title,
body.font-weight-semibold .bookmark-link {
  font-weight: var(--font-weight-semibold);
}

/* Aplicar o peso da fonte normal (padrão) */
body:not(.font-weight-semibold) .folder-title,
body:not(.font-weight-semibold) .bookmark-link {
  font-weight: var(--font-weight-normal);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
  -webkit-user-select: none;
}

/* ==============================================
   2. SCROLLBARS
   ============================================== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover);
}

::-webkit-scrollbar-button {
  display: none;
}

/* ==============================================
   3. LAYOUT PRINCIPAL
   ============================================== */
body {
  font-family: "interfont", 'Graphik', -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  width: 795px;
  height: 595px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: none;
}

/* Estilos para quando a extensão é aberta em uma nova aba */
body.tab-mode {
  width: auto !important;
  height: 100vh !important;
  overflow: hidden;
}

body.tab-mode main {
  flex: 1;
  margin-bottom: var(--footer-height);
  overflow: hidden;
}

body.tab-mode .content-container {
  height: calc(100vh - var(--footer-height));
  overflow: hidden;
}

body.tab-mode #folderCheckboxes.scrollable-list {
  overflow-y: scroll !important; /* Sempre exibir scroll na coluna 1 */
  overflow-x: hidden !important;
  height: calc(100% - 140px) !important;
  margin-bottom: 10px !important;
}

body.tab-mode #bookmarksContainer.scrollable-list {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: calc(100% - 140px) !important;
  margin-bottom: 10px !important;
}

/* ===== MENU DE CONTEXTO ===== */

.context-menu {
  position: fixed;
  background: var(--content-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  min-width: 200px;
  max-width: 300px;
  padding: 4px 0;
  font-size: 14px;
  user-select: none;
}

.context-options {
  display: block;
}

.context-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  color: var(--text-color);
}

.context-item:hover {
  background-color: var(--hover-bg);
}

.context-item:active {
  background-color: var(--active-bg);
}

.context-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.context-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.context-separator {
  height: 1px;
  background-color: var(--border-color);
  margin: 4px 0;
}

/* Tema escuro para menu de contexto */
body.dark-theme .context-menu {
  background: var(--content-bg);
  border-color: var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

body.dark-theme .context-item {
  color: var(--text-color);
}

body.dark-theme .context-item:hover {
  background-color: var(--hover-bg);
}

body.dark-theme .context-separator {
  background-color: var(--border-color);
}

/* ===== DIÁLOGO DE EDIÇÃO ===== */

.edit-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
}

.edit-dialog-content {
  background: var(--content-bg);
  border-radius: 8px;
  padding: 20px;
  min-width: 400px;
  max-width: 500px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.edit-dialog-content h3 {
  margin: 0 0 16px 0;
  color: var(--text-color);
  font-size: 18px;
  font-weight: 600;
}

.edit-field {
  margin-bottom: 16px;
}

.edit-field label {
  display: block;
  margin-bottom: 4px;
  color: var(--text-color);
  font-weight: 500;
}

.edit-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--content-bg);
  color: var(--text-color);
  font-size: 14px;
  box-sizing: border-box;
}

.edit-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.edit-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 20px;
}

.primary-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.15s ease;
}

.primary-btn:hover {
  background: var(--primary-hover);
}

.secondary-btn {
  background: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.15s ease;
}

.secondary-btn:hover {
  background: var(--hover-bg);
  border-color: var(--primary);
}

/* Tema escuro para diálogo */
body.dark-theme .edit-dialog-content {
  background: var(--content-bg);
}

body.dark-theme .edit-input {
  background: var(--content-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

body.dark-theme .edit-input:focus {
  border-color: var(--primary);
}

main {
  flex: 1;
  overflow: hidden;
  padding: 0;
  margin-bottom: var(--footer-height);
  display: flex;
  flex-direction: column;
  background-color: var(--content-bg);
}

.content-container {
  display: flex;
  height: calc(100vh - var(--footer-height));
  max-height: none;
  width: 100%;
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: var(--content-bg);
}

/* Header fixo */
.fixed-header {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-color);
  padding: 10px 16px;
  z-index: 100;
  border-bottom: 1px solid var(--border-color);
  height: var(--header-height);
}

.counts-container {
  display: flex;
  justify-content: space-between;
  gap: 0px;
}

.count-section {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

.count-section h2 {
  margin-left: 0;
  margin-bottom: 8px;
}

/* Área de rolagem */
.scrollable-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100% - 140px);
  padding: 5px;
  margin-bottom: 10px;
  background-color: var(--content-bg);
  border-radius: 4px;
  will-change: transform;
}

/* Otimizações para modo de redimensionamento */
body.resizing {
  cursor: col-resize !important;
  user-select: none;
}

body.resizing * {
  pointer-events: none !important;
}

body.resizing .separator,
body.resizing #separator-ghost {
  pointer-events: auto !important;
}

body.resizing .folders-column,
body.resizing .bookmarks-column {
  transition: none !important;
  will-change: contents;
  contain: layout style;
}

/* Desabilitar animações durante redimensionamento */
body.resizing * {
  animation: none !important;
  transition: none !important;
}

/* ==============================================
   4. CABEÇALHOS E COLUNAS
   ============================================== */
/* Colunas com cabeçalhos */
.folders-column, .bookmarks-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: var(--min-column-width);
  max-width: var(--max-column-width);
  contain: layout style;
  position: relative;
  height: 100%;
  background-color: var(--content-bg);
}

/* Coluna de pastas com largura fixa */
.folders-column {
  flex: 0 0 auto;
  width: 50%;
  z-index: 1;
  height: 100%;
  background-color: inherit;
}

body.dark-theme .folders-column {
  background-color: var(--folders-column-bg);
}

body.dark-theme .column-header {
  background-color: var(--folders-column-bg);
}

body.dark-theme #folderCheckboxes {
  background-color: var(--folders-column-bg);
}

/* Coluna de favoritos com posicionamento absoluto para sobreposição */
.bookmarks-column {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 50%;
  z-index: 10;
  background-color: var(--content-bg);
  height: 100%;
}

/* Cabeçalho da coluna */
.column-header {
  background-color: var(--content-bg);
  padding: 10px;
  margin-bottom: 0;
}

.column-header h2 {
  margin: 0;
  margin-bottom: 4px;
  font-size: 16px;
}

.column-header h3 {
  margin: 0;
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
}

/* Cabeçalho da pasta com os botões */
.folder-header {
  display: flex;
  justify-content: space-between;
  margin: 5px 5px 0px 10px;
  padding: 0;
  align-items: center;
  background-color: inherit;
  border: none;
}

/* Contêiner para os botões de selecionar/desselecionar */
.folder-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* ==============================================
   5. SEPARADOR
   ============================================== */
/* Separador com z-index maior que as colunas */
.separator {
  position: absolute;
  width: 6px;
  height: 100%;
  cursor: col-resize;
  background-color: var(--hover-overlay);
  border: none;
  border-radius: 0;
  margin: 0;
  padding: 0;
  z-index: 20;
  top: 0;
  transition: none;
}

body.dark-theme .separator {
  background-color: rgba(255, 255, 255, 0.05);
}

.separator::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 50px;
  background-color: rgba(0, 0, 0, 0.45);
  border-radius: 0;
  transform: translate(-50%, -50%);
  opacity: 0.9;
  transition: none;
  box-shadow: none;
}

body.dark-theme .separator::after {
  background-color: rgba(255, 255, 255, 0.25);
}

.separator::before {
  content: "⋮";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7);
  z-index: 2;
  pointer-events: none;
}

.separator:hover::after {
  background-color: var(--accent, #0078D4);
  height: 50px;
  width: 6px;
  opacity: 1;
  box-shadow: none;
}

.separator:focus {
  outline: none;
}

.separator:focus::after {
  background-color: var(--accent, #0078D4);
  height: 50px;
  width: 6px;
  opacity: 1;
  box-shadow: none;
}

.separator:active::after {
  background-color: var(--accent-hover, #005a9e);
  width: 6px;
  height: 50px;
  transform: translate(-50%, -50%);
}

/* Garantir que o separador esteja sempre acessível */
#mainSeparator {
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  appearance: none;
  -webkit-appearance: none;
  background-image: none;
  z-index: 50;
  border-radius: 0;
}

/* Melhorar foco para acessibilidade */
#mainSeparator:focus::after {
  background-color: var(--accent, #0078d4);
  box-shadow: none;
}

/* Estado de foco para acessibilidade sem mouse */
#mainSeparator:focus-visible {
  outline: 2px solid var(--accent, #0078d4);
  outline-offset: 0;
  border-radius: 0;
}

/* Remover todas as animações */
.separator:hover::after,
.separator:focus::after,
.separator:active::after {
  animation: none;
}

/* Remover animações para os outros estados também */
.separator.reset-effect::after,
.separator.key-resize::after {
  animation: none;
  background-color: var(--accent, #0078D4);
}



#separator-ghost {
  position: absolute;
  width: 6px !important; /* Linha para indicar posição */
  height: calc(100% - var(--footer-height)) !important; /* Limitando a altura para não ultrapassar o footer */
  background-color: var(--accent, #0078D4);
  pointer-events: none !important;
  top: 0 !important;
  bottom: var(--footer-height) !important; /* Parando antes do footer */
  z-index: 20; /* Menor que o z-index do footer (100) */
  opacity: 1;
  transition: none;
  border-radius: 0;
}

/* Permitir que o separador ainda receba eventos mesmo durante redimensionamento */
#mainSeparator {
  pointer-events: auto !important;
}

/* ==============================================
   6. TIPOGRAFIA
   ============================================== */
h1, h2, h3 {
  margin-bottom: 10px;
  color: var(--text-color);
}

h1 {
  font-size: 18px;
}

/* Título específico no rodapé */
footer h1 {
  display: none;
}

@media (min-width: 800px) {
  footer h1 {
    display: block;
  }
}

h2 {
  font-size: 16px;
}

h3 {
  font-size: 14px;
  margin: 0;
  color: var(--text-color);
  font-weight: 500;
}

/* ==============================================
   7. FORMULÁRIOS E CONTROLES
   ============================================== */
/* Formulários e controles */
input[type="text"],
select,
button {
  padding: 8px;
  border-radius: 5px;
  border: 1px solid var(--border-color);
  font-size: 14px;
  margin-bottom: 10px;
}

/* Input de tipo search tem seu próprio estilo */
input[type="search"] {
  padding: 8px;
  font-size: 14px;
  border: none;
  outline: none;
  background-color: transparent;
  margin-bottom: 0;
  color: var(--input-text);
}

/* Estilos comuns para botões */
button {
  background-color: var(--accent);
  color: #fff;
  border: none;
  cursor: pointer;
  transition: none;
}

button:hover {
  background-color: var(--item-hover-bg);
}

/* Barra de pesquisa */
.search {
  position: relative;
  margin: 10px 0;
  padding: 0 10px;
  width: 100%;
  background-color: inherit;
}

.search-container {
  position: relative;
  width: 100%;
  height: 36px;
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  transition: none;
  background-color: var(--input-bg);
}

.search-container:focus-within {
  border-color: var(--accent);
  box-shadow: 0 0 0 1px var(--accent);
}

.search-input {
  flex: 1;
  border: none;
  padding: 8px 10px;
  font-size: 14px;
  background-color: transparent;
  width: 100%;
  color: var(--text-color);
  outline: none;
  margin-bottom: 0; /* Removendo margem inferior que estava afetando o layout */
}

/* Estilos comuns para os botões de limpar de WebKit e Firefox */
.search-input::-webkit-search-cancel-button,
.search-input::-moz-search-clear-button {
  height: 16px;
  width: 16px;
  background-image: url("../img/icons/x.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-color: transparent;
  cursor: pointer;
  margin-right: 2px;
  margin-left: 8px;
  padding-left: 2px;
  opacity: 0.7;
  transition: opacity 0.2s;
  -webkit-appearance: none;
  appearance: none;
}

/* Estilos específicos para WebKit */
.search-input::-webkit-search-cancel-button {
  -webkit-appearance: none;
  visibility: visible !important;
  display: inline-block !important;
}

/* Estilos específicos para Firefox */
.search-input::-moz-search-clear-button {
  -moz-appearance: none;
  visibility: visible !important;
  display: inline-block !important;
}

.search-input::-webkit-search-cancel-button:hover,
.search-input::-moz-search-clear-button:hover {
  opacity: 1;
}

/* Adiciona suporte ao botão de limpar para Firefox e outros navegadores */
.search-input[data-show-clear="true"] {
  padding-right: 20px;
}

/* Estilos para navegadores que não suportam pseudo-elementos para search input */
@supports not ((-webkit-appearance: none) or (-moz-appearance: none)) {
  .search-container {
    position: relative;
  }
  
  .search-container::after {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-image: url("../img/icons/x.png");
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.7;
    cursor: pointer;
    display: none;
  }
  
  .search-container:has(input[value]:not([value=""])) + .search-container::after {
    display: block;
  }
}

/* Estilos para o botão de limpar personalizado */
.clear-search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background: url("../img/icons/x.png") no-repeat center;
  background-size: contain;
  border: none;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
  z-index: 10;
  display: none;
  padding: 0;
  margin: 0;
  color: var(--input-text);
  font-size: 18px;
}

.clear-search-btn:hover {
  opacity: 1;
}

/* Estilos para o dropdown de cópia */
.copy-dropdown-container {
  position: relative;
  display: inline-block;
}

.copy-dropdown {
  position: absolute;
  bottom: 60px;
  right: 0;
  background-color: var(--dropdown-bg);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
  display: none;
  flex-direction: column;
  z-index: 101;
  overflow: hidden;
  animation: none;
  min-width: 180px;
  max-height: 300px;
  overflow-y: auto;
}

.copy-dropdown.show {
  display: flex;
}

.copy-option {
  padding: 12px 16px;
  cursor: pointer;
  transition: none;
  font-size: 14px;
  color: var(--text-color);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid var(--hover-overlay);
}

.copy-option:last-child {
  border-bottom: none;
}

.copy-option:hover {
  background-color: var(--item-hover-bg);
}

.copy-option:active {
  background-color: var(--item-hover-bg);
}

.copy-option svg {
  width: 18px;
  height: 18px;
  opacity: 0.8;
  transition: opacity 0.2s;
  flex-shrink: 0;
}

.copy-option:hover svg {
  opacity: 1;
}

/* Estilo especial para favoritos de URLs internas do navegador */
.bookmark-item[data-internal-url="true"] {
  background-color: rgba(255, 251, 230, 0.6);
  border-left: 3px solid #FFD700;
}

.bookmark-item[data-internal-url="true"] .bookmark-link {
  color: #444;
}

.bookmark-item[data-internal-url="true"] .favicon {
  opacity: 1;
}

/* Cor para cada tipo de URL interna */
.bookmark-item[data-internal-url="chrome"] {
  border-left-color: #FFD700;
}

.bookmark-item[data-internal-url="edge"] {
  border-left-color: #FFD700;
}

.bookmark-item[data-internal-url="about"] {
  border-left-color: #FFD700;
}

.bookmark-item[data-internal-url="browser"] {
  border-left-color: #FFD700;
}

/* Menu dropdown para classificação */
.sort-dropdown {
  position: absolute;
  bottom: 60px !important;
  right: 0 !important;
  background: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  border-radius: 8px;
  z-index: 101;
  display: none;
  flex-direction: column;
  overflow: hidden;
  animation: none;
  min-width: 180px;
  max-height: 300px;
  overflow-y: auto;
  padding: 0;
}

.sort-dropdown::-webkit-scrollbar {
  width: 5px;
}

.sort-dropdown::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 10px;
}

.sort-dropdown.show {
  display: flex;
}

.sort-option {
  padding: 12px 16px;
  cursor: pointer;
  transition: none;
  font-size: 14px;
  color: var(--text-color);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid var(--hover-overlay);
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option:hover {
  background-color: var(--item-hover-bg);
}

.sort-option:active {
  background-color: var(--item-hover-bg);
}

.sort-option.active {
  font-weight: 600;
  color: var(--primary);
  background-color: var(--active-option-bg);
}

.sort-option i {
  font-size: 16px;
  opacity: 0.8;
}

.sort-option svg {
  width: 18px;
  height: 18px;
  opacity: 0.8;
  flex-shrink: 0;
}

/* Custom contextmenu */
.context-menu {
  position: absolute;
  z-index: 1000;
  background-color: var(--dropdown-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  color: var(--text-color);
}

.context-menu-item:hover {
  background-color: var(--item-hover-bg);
}

/* ==============================================
   8. ITENS DE BOOKMARK
   ============================================== */
/* Container estrutural para checkbox */
.checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
  padding: 4px;
  height: 100%;
}

/* Checkbox para favoritos */
.bookmark-checkbox {
  width: 16px;
  height: 16px;
  accent-color: var(--accent);
  cursor: pointer;
  flex-shrink: 0;
  pointer-events: none; /* Igual às pastas - impedir clique direto no checkbox */
  pointer-events: none; /* Impedir que o checkbox responda a cliques diretamente */
}

/* Container estrutural para favicon */
.favicon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
  width: 20px;
  height: 100%;
}



/* Container para elementos da esquerda */
.left-container {
  display: flex;
  align-items: stretch; /* Faz os filhos ocuparem toda altura */
  flex-grow: 1;
  gap: 8px;
  min-width: 0;
  height: 100%;
}

/* Container para título e URL (dividem espaço 50/50) */
.text-container {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  min-width: 0;
  height: 100%;
}

/* Container de ações (botões) */
.actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* Container estrutural para título */
.title-container {
  display: flex;
  align-items: center;
  flex: 1; /* Ocupa 50% do espaço disponível */
  min-width: 0; /* Permite que o texto seja truncado */
  height: 100%; /* Preenche toda a altura */
  padding: 0 4px;
  cursor: pointer; /* Indica que é clicável */
}

/* Container para URL */
.url-container {
  display: flex;
  align-items: center;
  flex: 1; /* Ocupa 50% do espaço disponível */
  min-width: 0;
  height: 100%;
  padding: 0 4px;
  font-size: 11px;
  color: #666;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 0.8;
}

/* URL container no tema escuro */
body.dark-theme .url-container {
  color: #aaa;
}



/* Estilo específico para o item quando selecionado */
.bookmark-item:has(.bookmark-checkbox:checked) {
  background-color: #efeef6;
  /* border-color: var(--yellow-accent); */
}

/* Itens de favoritos */
.bookmark-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  margin-bottom: 2px;
  background-color: var(--item-bg);
  border: none;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 13px;
  font-family: 'Segoe UI', Roboto, Geneva, Verdana, sans-serif;
  line-height: 1.2;
  height: var(--item-height-normal);
  gap: 8px;
  position: relative; /* Necessário para os drop indicators */
}

.bookmark-item:hover {
  background-color: var(--item-hover-bg);
  box-shadow: 0 3px 6px rgba(0,0,0,0.12);
}

.bookmark-item.selected {
  background-color: #efeef6;
}

/* Estilo especial para favoritos de URLs internas do navegador */
.bookmark-item[data-internal-url="true"] {
  border-left: 3px solid transparent;
  /* box-shadow: 0 2px 4px rgba(255, 215, 0, 0.15); */
}

/* Ajuste para o tema escuro */
body.dark-theme .bookmark-item {
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

body.dark-theme .bookmark-item:hover {
  box-shadow: 0 3px 6px rgba(0,0,0,0.3);
}

body.dark-theme .bookmark-item.selected {
}

body.dark-theme .bookmark-item[data-internal-url="true"] {
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.1);
}

.bookmark-item span {
  flex-grow: 1;
  font-size: 14px;
  margin-right: 10px;
  word-break: break-word;
}



/* Estilo padrão para o link dos favoritos */
a.bookmark-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  min-width: 0;
  height: 100%; /* Preenche toda a altura do title-container */
  width: 100%; /* Preenche toda a largura do title-container */
}

a.bookmark-link:hover {
  /* text-decoration: underline; */
}

.bookmark-item:hover a.bookmark-link {
  /* text-decoration: underline; */
}

a.bookmark-link:visited {
  color: var(--link-visited-color);
}

.favicon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  transition: none;
  border-radius: 3px;
}

/* Efeito visual para favicons carregando */
.favicon:not([src]),
.favicon[src=""] {
  opacity: 0.2;
}

.favicon[src^="../img/icons/star_blue_ext.png"] {
  opacity: 1; /* Estrela azul com opacidade total */
}

.favicon[src^="../img/icons/bookmark.png"] {
  opacity: 0.5;
}

/* Corpo do checkboxes */
#folderCheckboxes {
  flex: 1;
  overflow-y: scroll; /* Sempre exibir scroll */
  overflow-x: hidden;
  height: calc(100% - 140px);
  padding: 8px 15px 5px 5px; /* Adicionado padding superior para área de drop + ajuste para barra de rolagem */
  margin-bottom: 0px;
  background-color: var(--content-bg);
  border-radius: 4px;
  direction: rtl; /* Move a barra de rolagem para o lado esquerdo usando o truque de direction: rtl */
  /* 
    Usamos direction: rtl para mover a barra de rolagem para a esquerda,
    e depois direction: ltr nos elementos filhos para manter o texto na direção correta.
    Esta técnica é compatível com a maioria dos navegadores.
  */
}

/* Define a direção do texto de volta para o normal para os elementos dentro da coluna */
#folderCheckboxes > * {
  direction: ltr;
}

#bookmarksContainer {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100% - 140px);
  padding: 8px 10px 0px 10px; /* Adicionado padding superior para área de drop */
  margin-bottom: 0px;
  background-color: var(--content-bg);
  border-radius: 4px;
}

/* Estilo especial para favoritos de URLs internas do navegador */
.bookmark-item[data-internal-url="true"] {
  background-color: rgba(255, 251, 230, 0.6);
  border-left: 3px solid #FFD700;
}

.bookmark-item[data-internal-url="true"] .bookmark-link {
  color: #444;
}

.bookmark-item[data-internal-url="true"] .favicon {
  opacity: 1;
}

/* Cor para cada tipo de URL interna */
.bookmark-item[data-internal-url="chrome"] {
  border-left-color: #FFD700;
}

.bookmark-item[data-internal-url="edge"] {
  border-left-color: #FFD700;
}

.bookmark-item[data-internal-url="about"] {
  border-left-color: #FFD700;
}

.bookmark-item[data-internal-url="browser"] {
  border-left-color: #FFD700;
}

/* ==============================================
   9. PASTAS E OPÇÕES
   ============================================== */
/* Checkbox para pastas */
.folder-option {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 2px;
  border-radius: 4px;
  transition: none;
  background-color: var(--item-bg);
  box-shadow: 0 1px 2px rgba(0,0,0,0.02);
  height: var(--item-height-normal);
  text-align: left;
  margin-right: 0;
  margin-left: 0;
  padding: 0px 8px;
  position: relative; /* Necessário para os drop indicators */
}

.folder-option:hover {
  background-color: var(--item-hover-bg);
  border-color: var(--border-color);
}

body.dark-theme .folder-option {
  background-color: rgba(51, 51, 51, 0.6);
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  /* border: 1px solid rgba(255, 255, 255, 0.05); */
}

body.dark-theme .folder-option:hover {
  background-color: rgba(70, 70, 70, 0.7);
  border-color: rgba(255, 255, 255, 0.1);
}

.folder-checkbox {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: var(--accent);
  cursor: pointer;
  flex-shrink: 0; /* Impede que o checkbox encolha */
  pointer-events: none; /* Impedir que o checkbox responda a cliques diretamente */
}

/* Ícone de pasta */
.folder-title-container {
  display: flex;
  align-items: center;
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
}

.folder-icon {
  flex-shrink: 0;
  margin-right: 4px;
  opacity: 0.8;
  transition: none;
}

/* Melhorar visibilidade do ícone no modo escuro */
body.dark-theme .folder-icon path {
  fill: #b3b3b3;
  opacity: 0.9;
}

body.dark-theme .folder-icon path:first-child {
  fill: #666666;
  opacity: 0.3;
}

.folder-title {
  flex-grow: 1;
  text-align: left;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--folder-title-color);
  font-weight: var(--font-weight-normal);
  letter-spacing: 0.2px;
  text-decoration: none; /* Garante que não haja sublinhado por padrão */
}

/* Estilo para pasta selecionada com ícone colorido */
.folder-option:has(.folder-checkbox:checked) .folder-icon path {
  /* fill: #e5b122; */
  opacity: 1;
}

/* Cor de fundo para pasta selecionada */
.folder-option:has(.folder-checkbox:checked) .folder-icon path:first-child {
  /* fill: #e5b122; */
  opacity: 0.15;
}

/* Melhorar visibilidade do ícone selecionado no modo escuro */
body.dark-theme .folder-option:has(.folder-checkbox:checked) .folder-icon path {
  fill: #ffffff;
  opacity: 1;
}

body.dark-theme .folder-option:has(.folder-checkbox:checked) .folder-icon path:first-child {
  fill: #ffffff;
  opacity: 0.4;
}

/* Ajuste para recuo e estilo de subpastas */
.folder-nest {
  margin-left: calc(var(--indent-size) + 2px);
  position: relative;
}

.folder-nest::before {
  content: "";
  position: absolute;
  left: -6px;
  top: 0;
  height: 100%;
  width: 1px;
  background-color: var(--folder-nest-line);
  /* Garantir que a linha seja visível independente da direção do texto */
  direction: ltr;
}

/* Estilo para as pastas selecionadas */
/* .folder-option:has(.folder-checkbox:checked) {
  background-color: var(--folder-icon-bg);
  border-color: var(--yellow-accent);
  transition: none;
}

.folder-option:has(.folder-checkbox:checked) .folder-title {
  color: var(--folder-selected-text);
  font-weight: var(--font-weight-semibold);
}

body.dark-theme .folder-option:has(.folder-checkbox:checked) {
  background-color: var(--folder-icon-bg);
  border-color: var(--yellow-accent);
} */

/* Estilo para as divisórias no modo escuro */
body.dark-theme .folder-nest::before {
  background-color: var(--folder-nest-line);
}

.select-all-container {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  cursor: pointer;
}

.select-all-container input {
  margin: 0;
}

/* ==============================================
   10. AÇÕES E BOTÕES
   ============================================== */
/* Ações (ícones de abrir/remover) */
.actions {
  display: flex;
  gap: 5px;
  margin-left: auto;
}

.actions a,
.actions button {
  background: none;
  border: none;
  font-size: 16px;
  padding: 2px 4px;
  cursor: pointer;
  color: #444;
  transition: none;
  margin: 0;
}

.actions a:hover,
.actions button:hover {
  color: var(--accent);
}

/* Estilo para o botão de remoção com ícone X */
.remove-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  padding: 0 !important;
  border-radius: 3px;
  transition: none;
}

.remove-btn:hover {
  background-color: var(--hover-overlay);
}

.remove-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
  transition: none;
}

.remove-icon path {
  fill: #666;
}

.remove-btn:hover .remove-icon {
  opacity: 1;
}

.remove-btn:hover .remove-icon path {
  fill: #333;
}

/* Estilos específicos para o botão "Selecionar todas" */
.folder-header-btn,
.folder-header-btn {
  font-size: 13px;
  padding: 4px 10px;
  min-width: auto;
  white-space: nowrap;
  background-color: var(--primary);
  border: 1px solid var(--primary);
  color: white;
  transition: none;
  border-radius: 4px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  box-shadow: 0 1px 2px var(--hover-overlay);
  cursor: pointer;
}

.folder-header-btn:hover,
.folder-header-btn:focus {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

/* Estilo único para todos os botões do rodapé */
.button-footer, 
.copy-btn, 
.delete-btn {
  height: var(--button-height);
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: none;
  margin: 0;
  color: white;
  background-color: var(--primary);
}

.button-footer:hover,
.copy-btn:hover {
  background-color: var(--primary-hover);
}

/* Estilo específico para o botão de cópia com ícone */
.copy-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
  transition: none;
}

.copy-btn:hover .copy-icon,
.copy-btn.active .copy-icon {
  filter: brightness(0) invert(1) brightness(1.2);
}

/* Manter estilo específico para o botão de exclusão */
.delete-btn {
  background-color: #d9534f;
}

.delete-btn:hover {
  background-color: #c9302c;
}

.delete-icon {
  width: 18px;
  height: 18px;
  filter: brightness(0) invert(1);
}

/* Feedback toast for actions */
.action-feedback {
  position: fixed;
  left: 50%;
  bottom: calc(var(--footer-height) + 18px);
  transform: translateX(-50%);
  min-width: 220px;
  max-width: 80vw;
  background: var(--toast-bg);
  color: var(--toast-text);
  padding: 12px 28px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.18);
  font-size: 15px;
  font-weight: 500;
  z-index: 9999;
  opacity: 1;
  pointer-events: none;
  text-align: center;
  transition: opacity 0.3s ease-out;
}

/* Classe para ocultar o feedback com transição suave */
.action-feedback.hide {
  opacity: 0;
}

.action-feedback.success {
  background: #0078D4;
  color: #fff;
}
.action-feedback.error {
  background: #d9534f;
  color: #fff;
}
.action-feedback.info {
  background: #e5b122;
  color: #222;
}

/* ==============================================
   11. DROPDOWNS E MENUS
   ============================================== */
/* Menu dropdown para classificação */
.sort-dropdown {
  position: absolute;
  bottom: 60px !important;
  right: 0 !important;
  background: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  border-radius: 8px;
  z-index: 101;
  display: none;
  flex-direction: column;
  overflow: hidden;
  animation: none;
  min-width: 180px;
  max-height: 300px;
  overflow-y: auto;
  padding: 0;
}

.sort-dropdown::-webkit-scrollbar {
  width: 5px;
}

.sort-dropdown::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 10px;
}

.sort-option {
  padding: 12px 16px;
  cursor: pointer;
  transition: none;
  font-size: 14px;
  color: var(--text-color);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid var(--hover-overlay);
}
.sort-option:last-child {
  border-bottom: none;
}

.sort-option:hover {
  background-color: var(--item-hover-bg);
}
.sort-option:active {
  background-color: var(--item-hover-bg);
}

.sort-option.active {
  font-weight: 600;
  color: var(--primary);
  background-color: var(--active-option-bg);
}

.sort-option i {
  font-size: 16px;
  opacity: 0.8;
}

/* Custom contextmenu */
.context-menu {
  position: absolute;
  z-index: 1000;
  background-color: var(--dropdown-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.context-menu-item {
  padding: 8px 12px;
  cursor: pointer;
  color: var(--text-color);
}

.context-menu-item:hover {
  background-color: var(--item-hover-bg);
}

/* ==============================================
   12. CONTADORES E INDICADORES
   ============================================== */
/* Garantir que o contador de pastas selecionadas tenha um estilo consistente */
#selectedFoldersCount {
  color: var(--text-color);
  font-weight: 500;
  margin: 0;
  padding: 0;
  font-size: 14px;
  display: block;
  line-height: 1.4;
}

.column-header #selectedFoldersCount {
  margin-top: 4px;
  margin-bottom: 0;
}

#selectedFoldersCount.has-selected {
  color: #0392ff;
  font-weight: 600;
}

/* Contador de pastas exibidas */
#folderCount {
  color: var(--text-color);
  font-weight: 700;
  margin: 0;
  padding: 0;
  font-size: 16px;
  display: block;
  line-height: 1.4;
}

#folderCount.has-folders {
  color: var(--accent, #0078D4);
  font-weight: 700;
}

/* Contador de favoritos exibidos */
#bookmarksDisplayCount {
  color: #0392ff;
  font-weight: 700;
  margin: 0;
  padding: 0;
  font-size: 16px;
  display: block;
  line-height: 1.4;
}

#bookmarksDisplayCount.has-bookmarks {
  color: #0392ff;
  font-weight: 700;
}

/* Contador de favoritos selecionados */
#selectedBookmarksCount {
  color: #0392ff;
  font-weight: 500;
  margin: 0;
  padding: 0;
  font-size: 14px;
  display: block;
  line-height: 1.4;
}

.column-header #selectedBookmarksCount {
  margin-top: 4px;
  margin-bottom: 0;
}

#selectedBookmarksCount.has-selected {
  color: #0392ff;
  font-weight: 600;
}

/* ==============================================
   13. RODAPÉ
   ============================================== */
/* Rodapé */
footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: #e3e0e7;
  border-top: 1px solid var(--border-color);
  height: var(--footer-height);
  z-index: 100;
}

/* Footer no modo escuro */
body.dark-theme footer {
  background-color: #3c3b3d;
}

.footer-left {
  display: flex;
  align-items: center;
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.footer-right {
  display: flex;
  gap: 10px;
}



/* Tema escuro para drag */
body.dark-theme .drag-ghost {
  background: #6495ed;
  border: 3px solid #ffffff;
  color: #ffffff;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.6);
}

body.dark-theme .drop-indicator {
  background: #6495ed;
  box-shadow: 0 0 4px rgba(100, 149, 237, 0.5);
}

body.dark-theme .bookmark-item.selected {
  background-color: rgba(100, 149, 237, 0.2);
  border-left-color: #6495ed;
}

/* Estilo específico para o botão de mesclagem */
#mergeBtn {
  background-color: #e5b122;
  color: #000000;
}

#mergeBtn:hover {
  background-color: #d9a61d;
  color: #000000;
}

/* Diálogo de mesclagem de pastas */
.merge-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.merge-dialog-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.merge-dialog h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.target-folder-select {
  margin: 20px 0;
}

.target-folder-select select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  background-color: #f9f9f9;
}

.merge-options {
  margin: 15px 0;
}

.merge-options label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
}

.merge-options input[type="checkbox"] {
  margin-right: 8px;
}

.merge-dialog-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}

.merge-dialog-buttons button {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.merge-dialog-buttons .secondary-btn {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #333;
}

.merge-dialog-buttons .secondary-btn:hover {
  background-color: #e5e5e5;
}

.merge-dialog-buttons .primary-btn {
  background-color: #4285f4;
  border: 1px solid #3367d6;
  color: white;
}

.merge-dialog-buttons .primary-btn:hover {
  background-color: #3367d6;
}

.merge-dialog-buttons button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Estilo para o botão de alternância de tema */
#themeToggleBtn {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

#themeToggleBtn:hover {
  background-color: var(--hover-overlay);
}

body.dark-theme #themeToggleBtn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

#themeToggleBtn svg {
  width: 20px;
  height: 20px;
  stroke: currentColor;
  fill: none;
}

body.dark-theme .actions button path {
  fill: #b3b3b3;
}

/* Ajustar cores das ações no modo escuro */
body.dark-theme .actions a,
body.dark-theme .actions button {
  color: #b3b3b3;
}

body.dark-theme .actions a:hover,
body.dark-theme .actions button:hover {
  color: var(--accent);
}

body.dark-theme .remove-icon path {
  fill: #b3b3b3;
}

body.dark-theme .remove-btn:hover .remove-icon path {
  fill: #e0e0e0;
}

/* Estilo para botão de processamento */
.folder-header-btn.processing {
  background-color: #e6e6e6;
  color: #666;
  cursor: wait;
  position: relative;
  overflow: hidden;
}

/* Efeitos para quando o usuário solta o botão após clique */
.folder-header-btn:active,
.folder-header-btn:focus,
.folder-header-btn:focus-visible {
  outline: none;
  box-shadow: none;
}

/* Garantir que os estados visuais não persistam após o clique */
.folder-header-btn:active:not(.processing) {
  background-color: var(--primary);
  transition: background-color 0.1s ease-out;
}

/* Reiniciar estilo do botão quando o mouse sai depois de clicar */
.folder-header-btn:not(.processing):focus:not(:active) {
  background-color: var(--primary);
  outline: none;
  box-shadow: none;
}

/* Efeito de brilho para o botão de processamento */
.folder-header-btn.processing::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255,255,255,0) 0%, 
    rgba(255,255,255,0.3) 50%, 
    rgba(255,255,255,0) 100%);
  animation: processing-shine 1.5s infinite;
}

/* Animação do efeito de brilho */
@keyframes processing-shine {
  0% { left: -100%; }
  100% { left: 200%; }
}



