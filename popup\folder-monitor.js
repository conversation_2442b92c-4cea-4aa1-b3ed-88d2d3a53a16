/**
 * Monitor de Pastas - Solução Direta
 * 
 * Sistema simplificado que monitora mudanças nos bookmarks
 * diretamente no popup, sem depender do background script.
 */

window.FolderMonitor = window.FolderMonitor || {};

(function() {
    'use strict';

    // Estado do monitor
    let isMonitoring = false;
    let monitorInterval = null;
    let lastFolderSnapshot = new Map();
    let lastBookmarkSnapshot = new Map();

    // Configurações
    const CONFIG = {
        CHECK_INTERVAL: 1500, // Verificar a cada 1.5 segundos (mais responsivo)
        ENABLE_LOGS: true,
        ENABLE_DETAILED_LOGS: false // Logs detalhados apenas quando necessário
    };

    /**
     * Inicia o monitoramento
     */
    function startMonitoring() {
        if (isMonitoring) return;

        console.log('[FolderMonitor] Iniciando monitoramento...');
        
        // Criar snapshot inicial
        createInitialSnapshot();
        
        // Iniciar verificação periódica
        monitorInterval = setInterval(checkForChanges, CONFIG.CHECK_INTERVAL);
        isMonitoring = true;
        
        console.log('[FolderMonitor] Monitoramento ativo');
    }

    /**
     * Para o monitoramento
     */
    function stopMonitoring() {
        if (!isMonitoring) return;

        if (monitorInterval) {
            clearInterval(monitorInterval);
            monitorInterval = null;
        }
        
        isMonitoring = false;
        console.log('[FolderMonitor] Monitoramento parado');
    }

    /**
     * Cria snapshot inicial das pastas e bookmarks
     */
    function createInitialSnapshot() {
        chrome.bookmarks.getTree((tree) => {
            if (chrome.runtime.lastError) {
                console.error('[FolderMonitor] Erro ao criar snapshot:', chrome.runtime.lastError);
                return;
            }

            lastFolderSnapshot.clear();
            lastBookmarkSnapshot.clear();
            
            processTreeForSnapshot(tree[0].children);
            
            if (CONFIG.ENABLE_LOGS) {
                console.log(`[FolderMonitor] Snapshot criado: ${lastFolderSnapshot.size} pastas, ${lastBookmarkSnapshot.size} bookmarks`);
            }
        });
    }

    /**
     * Processa árvore de bookmarks para criar snapshot
     * @param {Array} nodes - Nós da árvore
     */
    function processTreeForSnapshot(nodes) {
        if (!nodes) return;

        nodes.forEach(node => {
            if (node.url) {
                // É um bookmark
                lastBookmarkSnapshot.set(node.id, {
                    id: node.id,
                    title: node.title,
                    url: node.url,
                    parentId: node.parentId,
                    index: node.index
                });
            } else {
                // É uma pasta
                lastFolderSnapshot.set(node.id, {
                    id: node.id,
                    title: node.title,
                    parentId: node.parentId,
                    index: node.index
                });
                
                // Processar filhos recursivamente
                if (node.children) {
                    processTreeForSnapshot(node.children);
                }
            }
        });
    }

    /**
     * Verifica mudanças comparando com snapshot anterior
     */
    function checkForChanges() {
        chrome.bookmarks.getTree((tree) => {
            if (chrome.runtime.lastError) {
                console.error('[FolderMonitor] Erro ao verificar mudanças:', chrome.runtime.lastError);
                return;
            }

            const currentFolders = new Map();
            const currentBookmarks = new Map();
            
            // Criar snapshot atual
            processTreeForCurrentSnapshot(tree[0].children, currentFolders, currentBookmarks);
            
            // Comparar e detectar mudanças
            detectFolderChanges(currentFolders);
            detectBookmarkChanges(currentBookmarks);
            
            // Atualizar snapshots
            lastFolderSnapshot = currentFolders;
            lastBookmarkSnapshot = currentBookmarks;
        });
    }

    /**
     * Processa árvore atual para comparação
     * @param {Array} nodes - Nós da árvore
     * @param {Map} folders - Map de pastas
     * @param {Map} bookmarks - Map de bookmarks
     */
    function processTreeForCurrentSnapshot(nodes, folders, bookmarks) {
        if (!nodes) return;

        nodes.forEach(node => {
            if (node.url) {
                bookmarks.set(node.id, {
                    id: node.id,
                    title: node.title,
                    url: node.url,
                    parentId: node.parentId,
                    index: node.index
                });
            } else {
                folders.set(node.id, {
                    id: node.id,
                    title: node.title,
                    parentId: node.parentId,
                    index: node.index
                });
                
                if (node.children) {
                    processTreeForCurrentSnapshot(node.children, folders, bookmarks);
                }
            }
        });
    }

    /**
     * Detecta mudanças nas pastas
     * @param {Map} currentFolders - Pastas atuais
     */
    function detectFolderChanges(currentFolders) {
        // Detectar pastas removidas
        for (const [folderId, folderData] of lastFolderSnapshot) {
            if (!currentFolders.has(folderId)) {
                console.log(`[FolderMonitor] Pasta removida: ${folderId} (${folderData.title})`);
                handleFolderRemoved(folderId, folderData);
            }
        }

        // Detectar pastas adicionadas
        for (const [folderId, folderData] of currentFolders) {
            if (!lastFolderSnapshot.has(folderId)) {
                console.log(`[FolderMonitor] Pasta adicionada: ${folderId} (${folderData.title})`);
                handleFolderAdded(folderId, folderData);
            }
        }

        // Detectar pastas alteradas (título, posição, etc.)
        for (const [folderId, folderData] of currentFolders) {
            const oldData = lastFolderSnapshot.get(folderId);
            if (oldData) {
                // Verificar mudança de título
                if (oldData.title !== folderData.title) {
                    console.log(`[FolderMonitor] Pasta renomeada: ${folderId} (${oldData.title} → ${folderData.title})`);
                    handleFolderRenamed(folderId, folderData, oldData);
                }

                // Verificar mudança de posição (parentId ou index)
                if (oldData.parentId !== folderData.parentId || oldData.index !== folderData.index) {
                    if (CONFIG.ENABLE_DETAILED_LOGS) {
                        console.log(`[FolderMonitor] Pasta movida: ${folderId} (${folderData.title})`);
                        console.log(`  Parent: ${oldData.parentId} → ${folderData.parentId}`);
                        console.log(`  Index: ${oldData.index} → ${folderData.index}`);
                    }
                    handleFolderMoved(folderId, folderData, oldData);
                }
            }
        }
    }

    /**
     * Detecta mudanças nos bookmarks
     * @param {Map} currentBookmarks - Bookmarks atuais
     */
    function detectBookmarkChanges(currentBookmarks) {
        // Detectar bookmarks removidos
        for (const [bookmarkId, bookmarkData] of lastBookmarkSnapshot) {
            if (!currentBookmarks.has(bookmarkId)) {
                console.log(`[FolderMonitor] Bookmark removido: ${bookmarkId} (${bookmarkData.title})`);
                handleBookmarkRemoved(bookmarkId, bookmarkData);
            }
        }

        // Detectar bookmarks adicionados
        for (const [bookmarkId, bookmarkData] of currentBookmarks) {
            if (!lastBookmarkSnapshot.has(bookmarkId)) {
                console.log(`[FolderMonitor] Bookmark adicionado: ${bookmarkId} (${bookmarkData.title})`);
                handleBookmarkAdded(bookmarkId, bookmarkData);
            }
        }
    }

    /**
     * Trata remoção de pasta
     * @param {string} folderId - ID da pasta removida
     * @param {Object} folderData - Dados da pasta
     */
    function handleFolderRemoved(folderId, folderData) {
        // Remover da seleção se estiver selecionada
        if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.has(folderId)) {
            selectedFolderIds.delete(folderId);
            
            // Atualizar contador
            const selectedCountEl = document.getElementById("selectedFoldersCount");
            if (selectedCountEl) {
                selectedCountEl.textContent = `Selecionadas: ${selectedFolderIds.size}`;
                if (selectedFolderIds.size === 0) {
                    selectedCountEl.classList.remove("has-selected");
                }
            }
        }

        // Remover elemento da interface
        const folderCheckbox = document.querySelector(`input[value="${folderId}"]`);
        if (folderCheckbox) {
            const folderOption = folderCheckbox.closest('.folder-option');
            if (folderOption) {
                folderOption.remove();
                console.log(`[FolderMonitor] Elemento da pasta ${folderId} removido da interface`);
            }
        }

        // Remover favoritos desta pasta da coluna 2
        const bookmarkElements = document.querySelectorAll(`[data-folder="${folderId}"]`);
        bookmarkElements.forEach(element => element.remove());

        // Limpar cache
        if (typeof folderBookmarkMap !== 'undefined') {
            folderBookmarkMap.delete(folderId);
        }

        // Atualizar contadores
        if (typeof updateFolderCount === 'function') {
            updateFolderCount();
        }
        if (typeof updateBookmarkCount === 'function') {
            updateBookmarkCount();
        }

        // Feedback visual
        if (typeof showActionFeedback === 'function') {
            showActionFeedback(`Pasta "${folderData.title}" foi removida`, 'warning');
        }
    }

    /**
     * Trata movimentação de pasta
     * @param {string} folderId - ID da pasta movida
     * @param {Object} newData - Novos dados da pasta
     * @param {Object} oldData - Dados antigos da pasta
     */
    function handleFolderMoved(folderId, newData, oldData) {
        console.log(`[FolderMonitor] Pasta movida: ${folderId} (${newData.title})`);

        // Recarregar lista de pastas para refletir nova estrutura
        if (window.DynamicFolderRenderer && typeof window.DynamicFolderRenderer.forceReload === 'function') {
            window.DynamicFolderRenderer.forceReload();
        } else if (typeof loadBookmarkFolders === 'function') {
            loadBookmarkFolders();
        }

        if (typeof showActionFeedback === 'function') {
            showActionFeedback(`Pasta "${newData.title}" foi movida`, 'info');
        }
    }

    /**
     * Trata adição de pasta
     * @param {string} folderId - ID da pasta adicionada
     * @param {Object} folderData - Dados da pasta
     */
    function handleFolderAdded(folderId, folderData) {
        console.log(`[FolderMonitor] Processando adição de pasta: ${folderId} (${folderData.title})`);

        // Primeiro tentar usar o DynamicFolderRenderer se disponível
        if (window.DynamicFolderRenderer && typeof window.DynamicFolderRenderer.forceReload === 'function') {
            console.log('[FolderMonitor] Usando DynamicFolderRenderer para recarregar');
            window.DynamicFolderRenderer.forceReload();
        }
        // Fallback para loadBookmarkFolders
        else if (typeof loadBookmarkFolders === 'function') {
            console.log('[FolderMonitor] Usando loadBookmarkFolders para recarregar');
            loadBookmarkFolders();
        }
        // Último fallback: recarregar manualmente
        else {
            console.log('[FolderMonitor] Usando fallback manual para recarregar');
            chrome.bookmarks.getTree((tree) => {
                if (chrome.runtime.lastError) {
                    console.error('[FolderMonitor] Erro no fallback:', chrome.runtime.lastError);
                    return;
                }
                const roots = tree[0].children;
                const container = document.getElementById("folderCheckboxes");
                if (container && typeof populateFolderCheckboxes === 'function') {
                    populateFolderCheckboxes(roots, container);
                    if (typeof updateFolderCount === 'function') {
                        updateFolderCount();
                    }
                }
            });
        }

        if (typeof showActionFeedback === 'function') {
            showActionFeedback(`Nova pasta "${folderData.title}" detectada`, 'info');
        }
    }

    /**
     * Trata renomeação de pasta
     * @param {string} folderId - ID da pasta
     * @param {Object} newData - Novos dados
     * @param {Object} oldData - Dados antigos
     */
    function handleFolderRenamed(folderId, newData, oldData) {
        // Atualizar nome na interface usando a estrutura correta
        const folderCheckbox = document.querySelector(`input[value="${folderId}"]`);
        if (folderCheckbox) {
            const label = folderCheckbox.parentElement;
            if (label) {
                // Procurar pelo elemento .folder-title dentro do label
                const titleSpan = label.querySelector('.folder-title');
                if (titleSpan) {
                    titleSpan.textContent = newData.title;
                    console.log(`[FolderMonitor] Nome da pasta ${folderId} atualizado na interface: ${newData.title}`);
                } else {
                    // Fallback: procurar por qualquer span com texto
                    const spans = label.querySelectorAll('span');
                    for (const span of spans) {
                        if (span.textContent.trim() === oldData.title) {
                            span.textContent = newData.title;
                            console.log(`[FolderMonitor] Nome da pasta ${folderId} atualizado via fallback: ${newData.title}`);
                            break;
                        }
                    }
                }
            }
        }

        // Também notificar o DynamicFolderRenderer se disponível
        if (window.DynamicFolderRenderer && typeof window.DynamicFolderRenderer.handleFolderRenamed === 'function') {
            window.DynamicFolderRenderer.handleFolderRenamed(folderId, newData, oldData);
        }

        if (typeof showActionFeedback === 'function') {
            showActionFeedback(`Pasta renomeada: "${oldData.title}" → "${newData.title}"`, 'info');
        }
    }

    /**
     * Trata remoção de bookmark
     * @param {string} bookmarkId - ID do bookmark removido
     * @param {Object} bookmarkData - Dados do bookmark
     */
    function handleBookmarkRemoved(bookmarkId, bookmarkData) {
        // Remover elemento da interface
        const bookmarkElement = document.querySelector(`[data-id="${bookmarkId}"]`);
        if (bookmarkElement) {
            bookmarkElement.remove();
        }

        // Limpar cache
        if (typeof bookmarkElementCache !== 'undefined') {
            bookmarkElementCache.delete(bookmarkId);
        }

        // Atualizar contadores
        if (typeof updateBookmarkCount === 'function') {
            updateBookmarkCount();
        }
    }

    /**
     * Trata adição de bookmark
     * @param {string} bookmarkId - ID do bookmark adicionado
     * @param {Object} bookmarkData - Dados do bookmark
     */
    function handleBookmarkAdded(bookmarkId, bookmarkData) {
        // Se o bookmark foi adicionado em uma pasta selecionada, recarregar
        if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.has(bookmarkData.parentId)) {
            if (typeof reloadSelectedFolders === 'function') {
                reloadSelectedFolders();
            }
        }
    }

    /**
     * Força uma verificação imediata de mudanças
     */
    function forceCheck() {
        if (!isMonitoring) {
            console.warn('[FolderMonitor] Monitor não está ativo, iniciando...');
            startMonitoring();
            return;
        }

        console.log('[FolderMonitor] Forçando verificação imediata...');
        checkForChanges();
    }

    /**
     * Obtém estatísticas do monitor
     */
    function getStats() {
        return {
            monitoring: isMonitoring,
            folders: lastFolderSnapshot.size,
            bookmarks: lastBookmarkSnapshot.size,
            interval: CONFIG.CHECK_INTERVAL,
            lastCheck: new Date().toISOString()
        };
    }

    // API pública
    window.FolderMonitor = {
        start: startMonitoring,
        stop: stopMonitoring,
        getStats,
        createSnapshot: createInitialSnapshot,
        forceCheck: forceCheck
    };

    // Auto-iniciar quando o DOM estiver pronto
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(startMonitoring, 1000);
        });
    } else {
        setTimeout(startMonitoring, 1000);
    }

    // Comandos globais para debug
    window.startFolderMonitor = () => startMonitoring();
    window.stopFolderMonitor = () => stopMonitoring();
    window.folderMonitorStats = () => {
        const stats = getStats();
        console.table(stats);
        return stats;
    };
    window.forceCheckFolders = () => forceCheck();
    window.toggleFolderMonitorLogs = () => {
        CONFIG.ENABLE_DETAILED_LOGS = !CONFIG.ENABLE_DETAILED_LOGS;
        console.log(`[FolderMonitor] Logs detalhados: ${CONFIG.ENABLE_DETAILED_LOGS ? 'ATIVADOS' : 'DESATIVADOS'}`);
        return CONFIG.ENABLE_DETAILED_LOGS;
    };

    console.log('[FolderMonitor] Sistema carregado');

})();
