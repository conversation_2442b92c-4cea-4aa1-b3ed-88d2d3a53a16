// render.js

const bookmarkElementCache = new Map();
// Declarar a variável renderInProgress no escopo global
let renderInProgress = false;
// Flag para indicar que nenhuma renderização deve ocorrer
let blockRendering = false;
// Sistema de favicons movido para pasta favicons/
// As variáveis e funções agora estão disponíveis através de window.FaviconManager

// Ícone de estrela agora disponível através de window.FaviconManager.starIconDataUrl

// Funções de cache movidas para favicons/favicon-manager.js

// Funções de debug movidas para favicons/favicon-debug.js

// Funções de limpeza de cache movidas para favicons/favicon-manager.js

// Função de detecção de navegador movida para favicons/favicon-manager.js

// Função getFaviconUrl movida para favicons/favicon-manager.js

// Funções de carregamento de favicon movidas para favicons/favicon-loader.js

function createBookmarkElement(bookmark, folderId) {
  // Verificar se já existe no cache e tentar reutilizar
  if (bookmarkElementCache.has(bookmark.id)) {
    const cachedItem = bookmarkElementCache.get(bookmark.id);
    
    // Atualizar propriedades do elemento
    cachedItem.dataset.index = bookmark.index;
    cachedItem.dataset.folder = folderId;
    
    // Verificar se é URL interna e marcar adequadamente
    if (bookmark.url) {
      const url = bookmark.url.toLowerCase();
      if (url.startsWith('chrome://')) {
        cachedItem.dataset.internalUrl = 'chrome';
      } else if (url.startsWith('edge://')) {
        cachedItem.dataset.internalUrl = 'edge';
      } else if (url.startsWith('about:')) {
        cachedItem.dataset.internalUrl = 'about';
      } else if (url.startsWith('browser://')) {
        cachedItem.dataset.internalUrl = 'browser';
      } else {
        delete cachedItem.dataset.internalUrl;
      }
    }
    
    // Atualiza o status de seleção
    const isSelected = typeof getSelectedBookmarkIds === 'function' &&
                      getSelectedBookmarkIds().includes(bookmark.id);
    if (isSelected) {
      cachedItem.classList.add("selected");
      const checkbox = cachedItem.querySelector('.bookmark-checkbox');
      if (checkbox) checkbox.checked = true;
    } else {
      cachedItem.classList.remove("selected");
      const checkbox = cachedItem.querySelector('.bookmark-checkbox');
      if (checkbox) checkbox.checked = false;
    }
    
    // Atualizar o link e texto, mas sempre recarregar o favicon
    const titleLink = cachedItem.querySelector('.bookmark-link');
    if (titleLink) {
      titleLink.textContent = bookmark.title || bookmark.url;
      titleLink.href = bookmark.url;
      titleLink.title = bookmark.title || bookmark.url;
    }

    // Atualizar URL container
    const urlContainer = cachedItem.querySelector('.url-container');
    if (urlContainer) {
      urlContainer.textContent = bookmark.url;
    }



    // Atualizar favicon
    const faviconContainer = cachedItem.querySelector('.favicon-container');
    if (faviconContainer) {
      // Remover favicon antigo, se houver
      const oldFavicon = faviconContainer.querySelector('.favicon');
      if (oldFavicon) oldFavicon.remove();
      // Criar novo favicon
      const newFavicon = document.createElement("img");
      newFavicon.className = "favicon";
      newFavicon.alt = "";
      newFavicon.setAttribute("draggable", "false");

      // Carregar favicon usando o novo sistema
      window.FaviconSystem.loadFavicon(newFavicon, bookmark.url);
      faviconContainer.appendChild(newFavicon);
    }
    
    return cachedItem;
  }

  // Criar novo elemento se não estiver no cache - usando label igual às pastas
  const item = document.createElement("label");
  item.className = "bookmark-item";
  item.dataset.id = bookmark.id;
  item.dataset.index = bookmark.index;
  item.dataset.folder = folderId;

  // Habilitar drag and drop
  item.setAttribute("draggable", "true");
  
  // Verificar se é URL interna e marcar adequadamente
  if (bookmark.url) {
    const url = bookmark.url.toLowerCase();
    if (url.startsWith('chrome://')) {
      item.dataset.internalUrl = 'chrome';
    } else if (url.startsWith('edge://')) {
      item.dataset.internalUrl = 'edge';
    } else if (url.startsWith('about:')) {
      item.dataset.internalUrl = 'about';
    } else if (url.startsWith('browser://')) {
      item.dataset.internalUrl = 'browser';
    }
  }
  
  // Criar div estrutural para checkbox
  const checkboxContainer = document.createElement("div");
  checkboxContainer.className = "checkbox-container";
  checkboxContainer.setAttribute("draggable", "false");

  // Adicionar checkbox para seleção
  const checkbox = document.createElement("input");
  checkbox.type = "checkbox";
  checkbox.className = "bookmark-checkbox";
  checkbox.value = bookmark.id;
  checkbox.setAttribute("draggable", "false");

  // Desabilitar pointer-events para que o clique no item alterne o checkbox (igual às pastas)
  checkbox.style.pointerEvents = "none";

  // Event listener do checkbox igual às pastas
  checkbox.addEventListener("change", () => {
    console.log("Render.js: Checkbox change event disparado para", bookmark.id);

    // Usar a função de atualização de seleção do selection.js
    if (typeof updateBookmarkSelection === 'function') {
      updateBookmarkSelection(item, checkbox.checked);
    }

    if (typeof updateSelectedBookmarksCount === 'function') {
      updateSelectedBookmarksCount();
    }

    // Atualizar o último checkbox clicado para suporte ao Shift (igual às pastas)
    if (typeof updateLastClickedBookmarkCheckbox === 'function') {
      updateLastClickedBookmarkCheckbox(checkbox);
    }
  });

  checkboxContainer.appendChild(checkbox);
  
  // Aplica a classe 'selected' se o item estiver na lista de selecionados
  const isSelected = typeof getSelectedBookmarkIds === 'function' &&
                    getSelectedBookmarkIds().includes(bookmark.id);
  if (isSelected) {
    item.classList.add("selected", "sortable-selected");
    checkbox.checked = true;
  }

  // Criar div estrutural para favicon
  const faviconContainer = document.createElement("div");
  faviconContainer.className = "favicon-container";
  faviconContainer.setAttribute("draggable", "false");

  // Adiciona favicon
  const favicon = document.createElement("img");
  favicon.className = "favicon";
  favicon.alt = "";
  favicon.setAttribute("draggable", "false");

  // Carregar favicon usando o novo sistema
  window.FaviconSystem.loadFavicon(favicon, bookmark.url);

  faviconContainer.appendChild(favicon);

  // Criar div estrutural para o título (link)
  const titleContainer = document.createElement("div");
  titleContainer.className = "title-container";
  titleContainer.setAttribute("draggable", "false");

  const link = document.createElement("a");
  link.href = bookmark.url;
  link.target = "_blank";
  link.textContent = bookmark.title || bookmark.url;
  // Usar o título completo como tooltip, ou a URL se o título estiver vazio
  link.title = bookmark.title || bookmark.url;
  link.className = "bookmark-link";
  link.setAttribute("draggable", "false"); // Impede arrasto pelo link

  titleContainer.appendChild(link);

  // Criar div para exibir URL em cinza (como texto, não link)
  const urlContainer = document.createElement("div");
  urlContainer.className = "url-container";
  urlContainer.textContent = bookmark.url;
  urlContainer.setAttribute("draggable", "false");

  const removeBtn = document.createElement("button");
  removeBtn.className = "remove-btn";
  removeBtn.title = "Remover";
  removeBtn.setAttribute("draggable", "false");
  
  // Adiciona o ícone SVG diretamente no DOM
  const svgNS = "http://www.w3.org/2000/svg";
  const svgElem = document.createElementNS(svgNS, "svg");
  svgElem.setAttribute("width", "16");
  svgElem.setAttribute("height", "16");
  svgElem.setAttribute("viewBox", "0 0 20 20");
  svgElem.setAttribute("class", "remove-icon");
  svgElem.setAttribute("aria-hidden", "true");
  
  const path = document.createElementNS(svgNS, "path");
  path.setAttribute("d", "M4.09 4.22l.06-.07a.5.5 0 01.63-.06l.07.06L10 9.29l5.15-5.14a.5.5 0 01.63-.06l.07.06c.18.17.2.44.06.63l-.06.07L10.71 10l5.14 5.15c.18.17.2.44.06.63l-.06.07a.5.5 0 01-.63.06l-.07-.06L10 10.71l-5.15 5.14a.5.5 0 01-.63.06l-.07-.06a.5.5 0 01-.06-.63l.06-.07L9.29 10 4.15 4.85a.5.5 0 01-.06-.63l.06-.07-.06.07z");
  path.setAttribute("fill-rule", "nonzero");
  
  svgElem.appendChild(path);
  removeBtn.appendChild(svgElem);
  
  removeBtn.addEventListener("click", (e) => {
    e.stopPropagation();
    if (confirm("Deseja remover este favorito?")) {
      chrome.bookmarks.remove(bookmark.id, () => {
        item.remove();
        bookmarkElementCache.delete(bookmark.id);

        // Remover da seleção usando a API do selection.js
        if (typeof window.selectedBookmarkIds !== 'undefined') {
          window.selectedBookmarkIds.delete(bookmark.id);
        }
        if (typeof updateSelectedBookmarksCount === 'function') {
          updateSelectedBookmarksCount();
        }

        updateBookmarkCount();
      });
    }
  });

  const actions = document.createElement("div");
  actions.className = "actions";
  actions.append(removeBtn);

  // Criar container para título e URL (dividem espaço 50/50)
  const textContainer = document.createElement("div");
  textContainer.className = "text-container";
  textContainer.append(titleContainer, urlContainer);

  // Criar container para elementos da esquerda
  const leftContainer = document.createElement("div");
  leftContainer.className = "left-container";
  leftContainer.append(checkboxContainer, faviconContainer, textContainer);

  // Estrutura: container esquerdo + actions
  item.append(leftContainer, actions);

  // Função de seleção agora está em selection.js

  // REMOVIDO: Event listeners de clique movidos para sortable.js para evitar conflitos
  // O sortable.js agora gerencia todos os eventos de clique dos bookmarks

  // O titleContainer deve abrir o link quando clicado, não selecionar
  titleContainer.addEventListener("click", (e) => {
    // Permitir que o link seja aberto normalmente
    // Não chamar e.stopPropagation() para permitir que o evento chegue ao link
  });

  // Sistema de drag & drop removido
  
  // Armazenar no cache para reutilização futura
  bookmarkElementCache.set(bookmark.id, item);
  return item;
}

// Função setupDragEvents removida - Sistema de drag & drop desabilitado

// Função para limpar todos os favoritos visíveis e o contador
function clearAllBookmarks() {
  bookmarksContainer.innerHTML = "";
  updateBookmarksDisplayCount();
  
  // Não limpar o cache completamente, apenas remover elementos que não estão mais visíveis
  const visibleIds = new Set();
  document.querySelectorAll(".bookmark-item").forEach(el => {
    if (el.dataset.id) {
      visibleIds.add(el.dataset.id);
    }
  });
  
  // Manter apenas os itens que ainda estão visíveis
  for (const [id, element] of bookmarkElementCache.entries()) {
    if (!visibleIds.has(id)) {
      bookmarkElementCache.delete(id);
    }
  }
}

/**
 * Renderiza os favoritos no container especificado
 * @param {Array} bookmarks - Lista de favoritos para renderizar
 * @param {HTMLElement} container - Container onde os favoritos serão renderizados
 * @param {boolean} append - Se true, adiciona ao final; se false, substitui o conteúdo
 * @param {string} folderId - ID da pasta dos favoritos
 * @param {boolean} preserveCache - Se true, preserva o cache atual
 */
function renderBookmarks(bookmarks, container, append = false, folderId, preserveCache = false) {
  // Verificar se a renderização está bloqueada
  if (blockRendering) {
    console.log(`Renderização bloqueada para pasta ${folderId}, ignorando solicitação de ${bookmarks?.length || 0} favoritos`);
    return;
  }

  // Verificar se já existe uma renderização em andamento
  if (renderInProgress) {
    console.log("Renderização já em andamento, enfileirando nova solicitação");
    setTimeout(() => {
      // Verificar novamente se a renderização foi bloqueada entretanto
      if (blockRendering) {
        console.log(`Renderização bloqueada durante espera para pasta ${folderId}, cancelando solicitação enfileirada`);
        return;
      }
      renderBookmarks(bookmarks, container, append, folderId, preserveCache);
    }, 100);
    return;
  }

  renderInProgress = true;

  try {
    // Se não for para adicionar ao final, limpar o container
    if (!append) {
      // Remover apenas os elementos desta pasta
      if (folderId) {
        const elementsToRemove = container.querySelectorAll(`.bookmark-item[data-folder="${folderId}"]`);
        elementsToRemove.forEach(el => el.remove());
      } else {
        container.innerHTML = "";
      }
    }

    // Se não há favoritos, atualizar contagem e sair
    if (!bookmarks || bookmarks.length === 0) {
      updateBookmarksDisplayCount();
      renderInProgress = false;
      return;
    }
    
    // Contador de lotes agora gerenciado pelo sistema de favicons
    
    // Usar DocumentFragment para melhor performance
    const fragment = document.createDocumentFragment();
    
    // Filtrar apenas favoritos (com URL) e ordenar por índice
    const validBookmarks = bookmarks
      .filter(bookmark => bookmark.url)
      .sort((a, b) => (a.index || 0) - (b.index || 0));
    
    // Tamanho do lote para renderização
    const batchSize = 20;
    
    // Renderizar em lotes para não bloquear a UI
    renderBatch(validBookmarks, 0, batchSize, () => {
      // Atualizar a contagem após renderizar todos
      updateBookmarksDisplayCount();
      renderInProgress = false;
    });
    
    /**
     * Renderiza um lote de favoritos
     * @param {Array} bookmarkBatch - Lote de favoritos
     * @param {number} startIndex - Índice inicial
     * @param {number} batchSize - Tamanho do lote
     * @param {Function} onComplete - Função chamada ao completar todos os lotes
     */
    function renderBatch(bookmarkBatch, startIndex, batchSize, onComplete) {
      // Verificar se ainda há favoritos para renderizar
      if (startIndex >= bookmarkBatch.length) {
        if (onComplete) onComplete();
        return;
      }
      
      // Calcular o índice final do lote atual
      const endIndex = Math.min(startIndex + batchSize, bookmarkBatch.length);
      
      // Criar um fragmento para este lote
      const batchFragment = document.createDocumentFragment();
      
      // Renderizar este lote
      for (let i = startIndex; i < endIndex; i++) {
        const bookmark = bookmarkBatch[i];
        
        // Verificar se o bookmark é válido
        if (!bookmark || !bookmark.id) continue;
        
        try {
          const element = createBookmarkElement(bookmark, folderId);
          if (element) {
            batchFragment.appendChild(element);
          }
        } catch (error) {
          console.error("Erro ao criar elemento de favorito:", error);
        }
      }
      
      // Adicionar o fragmento ao container
      container.appendChild(batchFragment);
      
      // Agendar o próximo lote usando requestAnimationFrame para melhor performance
      requestAnimationFrame(() => {
          renderBatch(bookmarkBatch, endIndex, batchSize, onComplete);
      });
    }
  } catch (error) {
    console.error("Erro ao renderizar favoritos:", error);
    renderInProgress = false;
  }
}

function updateBookmarksDisplayCount() {
  try {
    // Verificação rigorosa: se não há pastas selecionadas, zerar contador
    if (selectedFolderIds.size === 0) {
      const countEl = document.getElementById("bookmarksDisplayCount");
      countEl.textContent = "Favoritos exibidos: 0";
      countEl.classList.remove("has-bookmarks");
      
      // Garantir que não haja elementos visíveis
      if (bookmarksContainer.children.length > 0) {
        clearAllBookmarks();
      }
      return;
    }
    
    // Conta apenas os itens realmente visíveis e de pastas selecionadas
    const visibleItems = document.querySelectorAll(".bookmark-item");
    const total = Array.from(visibleItems).filter(item => 
      item.style.display !== "none" && 
      // Verifica se o item é da pasta selecionada
      selectedFolderIds.has(item.dataset.folder)
    ).length;
    
    // Atualiza o contador principal
    const countEl = document.getElementById("bookmarksDisplayCount");
    countEl.textContent = `Favoritos exibidos: ${total === 0 ? '0' : total}`;
      
      // Adiciona classe visual quando há favoritos exibidos
      if (total > 0) {
      countEl.classList.add("has-bookmarks");
      } else {
      countEl.classList.remove("has-bookmarks");
    }
    
    // Verificação adicional: se o contador é zero, mas há elementos visíveis, há inconsistência
    // Limpar tudo para garantir a sincronização
    if (total === 0 && bookmarksContainer.children.length > 0) {
      bookmarksContainer.innerHTML = '';
    }
  } catch (error) {
    console.error("Erro ao atualizar contagem de favoritos:", error);
    // Em caso de erro, zera o contador e limpa tudo
    const countEl = document.getElementById("bookmarksDisplayCount");
    countEl.textContent = "Favoritos exibidos: 0";
    countEl.classList.remove("has-bookmarks");
    
    bookmarksContainer.innerHTML = '';
  }
}

// Adicionar a função updateFolderContents
/**
 * Atualiza o conteúdo da pasta selecionada
 * @param {string} folderId - ID da pasta a ser carregada
 * @param {boolean} reuseCache - Se deve reutilizar elementos em cache
 */
function updateFolderContents(folderId, reuseCache = true) {
  if (blockRendering) {
    console.log("Renderização bloqueada, ignorando updateFolderContents");
    return;
  }
  
  // Evitar múltiplas renderizações simultâneas
  if (renderInProgress) {
    console.log("Renderização já em andamento, enfileirando nova atualização");
    setTimeout(() => updateFolderContents(folderId, reuseCache), 100);
    return;
  }
  
  renderInProgress = true;
  
  chrome.bookmarks.getChildren(folderId, (children) => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao obter favoritos:", chrome.runtime.lastError);
      renderInProgress = false;
      return;
    }
    
    // Filtrar apenas favoritos com URL e ordenar por índice
    const bookmarks = children
      .filter(bookmark => bookmark.url)
      .sort((a, b) => (a.index || 0) - (b.index || 0));

    renderBookmarks(bookmarks, folderId, reuseCache);
    renderInProgress = false;
  });
}