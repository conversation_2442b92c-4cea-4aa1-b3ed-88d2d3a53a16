<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurações - Bookmark Folder Merger</title>
    <style>
        :root {
            --primary-color: #0078d4;
            --secondary-color: #0078d4;
            --accent-color: #005a9e;
            --text-color: #333;
            --light-text: #666;
            --bg-color: #f5f7fa;
            --card-bg: #ffffff;
            --border-color: #dce0e8;
            --hover-color: #eef2f9;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 880px;
            margin: 0 auto;
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 25px;
        }

        h1 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 15px;
            margin-top: 0;
            text-align: center;
        }

        h2 {
            color: var(--secondary-color);
            margin-top: 25px;
            text-align: center;
        }

        .option-group {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: #ffffff;
        }

        /* Estilos para centralização das seções específicas */
        .option-group.centered {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .option-group.centered .option-row {
            justify-content: center;
            max-width: 600px;
            width: 100%;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .option-group.centered .option-container {
            justify-content: center;
            max-width: 500px;
            align-items: center;
        }

        .option-group.centered .option-label {
            text-align: center;
            flex: none;
            width: 100%;
            margin-bottom: 10px;
        }

        .option-group.centered .option-control {
            justify-content: center;
            flex: none;
            width: auto;
            min-width: auto;
        }

        .option-group.centered select {
            min-width: 200px;
        }

        .option-group.centered input[type="checkbox"] {
            margin: 0;
            margin-left: 10px;
        }

        .option-group.centered .option-control {
            flex-direction: row;
            align-items: center;
        }

        .option-row {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .option-container {
            display: flex;
            align-items: flex-start;
            flex-wrap: wrap;
            width: 100%;
        }



        .option-label {
            flex: 0 0 230px;
            font-weight: 500;
            padding-top: 8px;
        }

        .option-control {
            flex: 1;
            min-width: 280px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }

        select {
            width: 100%;
            padding: 10px 12px; /* Aumentado de 8px para 10px */
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 15px; /* Aumentado de 14px para 15px */
            height: 40px; /* Altura fixa para os dropdowns */
        }



        input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin-right: 8px;
            accent-color: #0078d4;
        }

        input[type="color"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 30px;
            height: 30px;
            padding: 0;
            border: 3px solid #777777;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
            background-color: transparent;
        }

        input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }
        
        input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 0px;
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: var(--accent-color);
        }

        .button-group {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
            position: sticky;
            bottom: 20px;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 15px 0;
            border-top: 1px solid var(--border-color);
            z-index: 100;
            bottom: 0px;
        }
        
        body.dark-theme .button-group {
            background-color: rgba(50, 50, 50, 0.9);
        }

        .reset-btn {
            background-color: #d9534f;
        }

        .reset-btn:hover {
            background-color: #c9302c;
        }

        .info-text {
            color: var(--light-text);
            font-size: 13px;
            margin-top: 5px;
        }

        .color-preview {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 1px solid #ccc;
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
        }
        
        /* Estilos para a notificação */
        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: #fff;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            transition: opacity 0.3s ease;
        }
        
        .notification.success {
            background-color: #28a745;
        }
        
        .notification.error {
            background-color: #dc3545;
        }
        
        .notification.info {
            background-color: #17a2b8;
        }
        
        .notification.warning {
            background-color: #ffc107;
            color: #212529;
        }

        /* Estilos para a visualização prévia */
        .style-preview {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
            overflow: hidden;
        }

        .preview-item {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }

        .preview-folder {
            background-color: white;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            padding: 5px 10px;
            border-radius: 4px;
            height: 35px;
            position: relative;
            margin: 5px 0;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .preview-bookmark {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            padding: 5px 10px;
            border-radius: 6px;
            height: 35px;
            position: relative;
            margin: 5px 0;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        
        /* Layout de colunas */
        .columns-container {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .column {
            flex: 1;
            min-width: 280px;
            margin-bottom: 15px;
        }
        
        .column-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--secondary-color);
            padding-bottom: 5px;
            border-bottom: 1px solid var(--border-color);
        }
        
        /* Botões de cores */
        .color-buttons {
            display: flex;
            gap: 8px;
            margin-top: 5px;
            flex-wrap: wrap;
            max-width: 100%;
        }
        
        .color-button {
            width: 35px;
            height: 35px;
            border-radius: 4px;
            border: 1px solid #ccc;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            position: relative;
            margin-bottom: 5px;
        }
        
        .color-button:hover {
            transform: scale(1.1);
            box-shadow: 0 0 5px rgba(0,0,0,0.2);
        }
        
        .color-button.selected {
            box-shadow: 0 0 0 2px var(--accent-color);
            transform: scale(1.05);
        }
        
        .no-transitions .color-button {
            transition: none;
        }
        
        /* Estilos para nested options */
        .nested-options {
            margin-left: 15px;
            border-left: 2px solid var(--border-color);
            padding-left: 15px;
        }
        
        /* Estilos salvos */
        .saved-styles-list {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .saved-style-item {
            padding: 8px 12px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .saved-style-item:last-child {
            border-bottom: none;
        }
        
        .saved-style-item:hover {
            background-color: #e8e8e8;
        }
        
        .saved-style-item.selected {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .style-action-buttons {
            display: flex;
            gap: 8px;
        }
        
        .preview-hover {
            margin-top: 5px;
            margin-bottom: 35px;
            transition: background-color 0.3s ease;
        }
        
        /* Novos estilos para personalização de tema */
        #theme-customization-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .theme-section {
            flex: 1;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
        }

        /* Theme section do tema claro */
        #style-preview-light .theme-section {
            border: 1px solid var(--border-color);
        }

        /* Theme section do tema escuro */
        #style-preview-dark .theme-section {
            border: 1px solid #6c6c6c;
        }

        .state-section {
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        .state-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 5px;
        }

        /* State section do tema claro */
        #style-preview-light .state-section {
            border-bottom: 1px solid #eee;
        }
        #style-preview-light .state-section:last-child {
            border-bottom: none;
        }

        /* State section do tema escuro */
        #style-preview-dark .state-section {
            border-bottom: 1px solid #6c6c6c;
        }
        #style-preview-dark .state-section:last-child {
            border-bottom: none;
        }

        .state-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.1em;
            color: var(--secondary-color);
            cursor: pointer;
            position: relative;
            padding-right: 20px; /* Espaço para o ícone */
            user-select: none; /* Torna o texto não selecionável */
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        /* State-title do tema escuro com cor branca */
        #style-preview-dark .state-title {
            color: #e0e0e0;
        }

        /* State-title com texto "Normal" deve ter margin-top: 10px */
        .state-title:first-child {
            margin-top: 10px;
        }
        .state-title::after {
            content: '−'; /* Símbolo de "recolher" */
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2em;
            font-weight: bold;
        }
        .state-section.collapsed .state-title::after {
            content: '+'; /* Símbolo de "expandir" */
        }
        .state-section.collapsed .state-controls-wrapper {
            display: none;
        }

        .style-property-group {
            margin-bottom: 15px;
        }
        
        .style-property-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .style-property-header label {
            font-weight: 500;
            margin-left: 8px;
        }

        .style-controls {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 5px 10px;
            align-items: center;
            padding-left: 25px;
            justify-items: start;
            justify-content: center;
        }

        .style-controls label {
            font-size: 0.9em;
            color: var(--light-text);
        }

        /* Labels dos style-controls do tema escuro com a mesma cor dos preview-group */
        #style-preview-dark .style-controls label {
            color: #e0e0e0;
        }

        .style-controls input[type="color"] {
            width: 30px;
            height: 30px;
        }
        
        .style-controls input[type="range"] {
            width: 85%;
        }

        .color-input-container {
            display: flex;
            align-items: center;
            gap: 8px;
            padding-right: 25px;
        }

        .hex-color-input {
            width: 70px !important; /* Sobrescreve o estilo geral de input */
            font-family: 'Courier New', Courier, monospace;
            text-transform: uppercase;
        }

        .style-preview-container {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            display: contents;
        }
        
        .theme-switcher {
            margin-bottom: 15px;
        }

        .preview-group {
            margin-bottom: 10px;
        }

        .preview-group > label {
            font-weight: 500;
            display: block;
            margin-bottom: 5px;
            color: var(--secondary-color);
        }

        #style-preview {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f5f5f5;
            transition: background-color 0.3s;
        }
        #style-preview-dark.dark {
             background-color: #333;
             color: #e0e0e0; /* Cor do texto para o tema escuro */
        }

        #style-preview-dark.dark .preview-group > label {
            color: #e0e0e0; /* Garante que o label também fique claro */
        }

        .preview-item {
            padding: 10px;
            border-radius: 4px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 13px;
            transition: all 0.2s ease-in-out;
        }

        .preview-columns {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .preview-column {
            flex: 1;
            min-width: 250px;
        }
        .preview-column h4 {
            text-align: center;
            color: var(--secondary-color);
            margin-bottom: 10px;
            font-weight: 600;
        }

        /* Estilos para seções expansíveis */
        .state-title {
            cursor: pointer;
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Configurações - Bookmark Folder Merger</h1>

        <div class="main-sections-container">
        <div class="option-group centered">
            <h2>Aparência</h2>
            
            <div class="option-row">
                <div class="option-container">
                    <div class="option-label">Tamanho da altura das listas</div>
                    <div class="option-control">
                        <select id="fontSize">
                            <option value="small">Compacto</option>
                            <option value="medium" selected>Normal</option>
                        </select>
                    </div>
                </div>
            </div>

        </div>
        
        <div class="option-group">
            <h2>Personalização de Interface</h2>
            <div class="style-preview-container">
                <div class="preview-columns">
                    <!-- Coluna Tema Claro -->
                    <div class="preview-column">
                        <div id="style-preview-light" class="style-preview">
                            <div class="preview-group">
                                <label>Normal</label>
                                <div id="preview-normal-light" class="preview-item">Item Normal</div>
                            </div>
                            <div class="preview-group">
                                <label>Selecionado</label>
                                <div id="preview-selected-light" class="preview-item">Item Selecionado</div>
                            </div>
                            <div class="preview-group">
                                <label>Hover</label>
                                <div id="preview-hover-light" class="preview-item preview-hover">Item Hover</div>
                            </div>
                            <!-- Theme section para tema claro será injetada aqui pelo JavaScript -->
                            <div id="theme-customization-light"></div>
                        </div>
                    </div>
                    <!-- Coluna Tema Escuro -->
                    <div class="preview-column">
                        <div id="style-preview-dark" class="style-preview dark">
                            <div class="preview-group">
                                <label>Normal</label>
                                <div id="preview-normal-dark" class="preview-item">Item Normal</div>
                            </div>
                            <div class="preview-group">
                                <label>Selecionado</label>
                                <div id="preview-selected-dark" class="preview-item">Item Selecionado</div>
                            </div>
                            <div class="preview-group">
                                <label>Hover</label>
                                <div id="preview-hover-dark" class="preview-item preview-hover">Item Hover</div>
                            </div>
                            <!-- Theme section para tema escuro será injetada aqui pelo JavaScript -->
                            <div id="theme-customization-dark"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="option-group centered">
            <h2>Comportamento</h2>
            <div class="option-row">
                <div class="option-container">
                    <div class="option-control">
                        <span>Confirmar antes de excluir favoritos</span>
                        <input type="checkbox" id="confirmDelete" checked>
                    </div>
                </div>
            </div>

            <div class="option-row">
                <div class="option-container">
                    <div class="option-control">
                        <span>Confirmar antes de mesclar pastas</span>
                        <input type="checkbox" id="confirmMerge" checked>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="option-group centered">
            <h2>Backup e Restauração</h2>
            <div class="option-row">
                <div class="option-container">
                    <div class="option-control">
                        <button id="exportBtn">Exportar configurações</button>
                    </div>
                </div>
            </div>

            <div class="option-row">
                <div class="option-container">
                    <div class="option-control">
                        <input type="file" id="importFile" accept=".json" style="display: none;">
                        <button id="importBtn">Importar configurações</button>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <div class="button-group">
            <button id="resetBtn" class="reset-btn">Restaurar padrões</button>
            <button id="hardResetBtn" class="reset-btn" style="background-color: #c9302c;">Reset total</button>
            <button id="saveBtn">Salvar configurações</button>
        </div>
    </div>

    <script src="options.js"></script>
</body>
</html>
