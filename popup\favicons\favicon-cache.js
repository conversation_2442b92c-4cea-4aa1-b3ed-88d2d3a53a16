/**
 * Sistema de Cache de Favicons
 * 
 * Gerencia o armazenamento persistente e em memória dos favicons,
 * incluindo limpeza automática e otimizações de performance.
 */

window.FaviconCache = window.FaviconCache || {};

(function() {
    'use strict';

    // Configurações específicas do cache
    const CACHE_CONFIG = {
        // Chaves de armazenamento
        FAVICON_CACHE_KEY: 'faviconCache_v2',
        CACHE_METADATA_KEY: 'faviconCacheMetadata_v2',
        
        // Limites de cache
        MAX_MEMORY_CACHE_SIZE: 500,
        MAX_STORAGE_CACHE_SIZE: 2000,
        
        // Tempos de expiração
        FAVICON_CACHE_DURATION: 7 * 24 * 60 * 60 * 1000, // 7 dias
        METADATA_CACHE_DURATION: 30 * 24 * 60 * 60 * 1000, // 30 dias
        
        // Configurações de limpeza
        CLEANUP_INTERVAL: 60 * 60 * 1000, // 1 hora
        BATCH_SIZE: 50, // Tamanho do lote para operações em massa
        
        // Configurações de performance
        DEBOUNCE_DELAY: 2000, // 2 segundos para salvar no storage
        MAX_CONCURRENT_LOADS: 10 // Máximo de carregamentos simultâneos
    };

    // Cache em memória para acesso ultra-rápido
    let memoryCache = new Map();
    
    // Metadados do cache (estatísticas, última limpeza, etc.)
    let cacheMetadata = {
        lastCleanup: 0,
        totalRequests: 0,
        cacheHits: 0,
        cacheMisses: 0,
        version: '2.0'
    };
    
    // Controle de operações assíncronas
    let saveTimeout = null;
    let cleanupTimeout = null;
    let isLoading = false;
    let loadingQueue = [];
    let activeLoads = 0;

    /**
     * Gera uma chave de cache normalizada
     * @param {string} url - URL original
     * @returns {string} Chave normalizada
     */
    function generateCacheKey(url) {
        if (!url) return null;
        
        try {
            const urlObj = new URL(url);
            // Usar apenas o hostname para agrupar favicons do mesmo domínio
            return urlObj.hostname.toLowerCase();
        } catch (error) {
            // Para URLs inválidas, usar a própria URL como chave
            return url.toLowerCase().trim();
        }
    }

    /**
     * Cria uma entrada de cache
     * @param {string} faviconUrl - URL do favicon
     * @param {string} originalUrl - URL original do site
     * @returns {Object} Entrada de cache
     */
    function createCacheEntry(faviconUrl, originalUrl) {
        return {
            faviconUrl,
            originalUrl,
            timestamp: Date.now(),
            accessCount: 1,
            lastAccess: Date.now()
        };
    }

    /**
     * Verifica se uma entrada de cache é válida
     * @param {Object} entry - Entrada do cache
     * @returns {boolean} True se válida
     */
    function isCacheEntryValid(entry) {
        if (!entry || !entry.timestamp) return false;
        
        const age = Date.now() - entry.timestamp;
        return age < CACHE_CONFIG.FAVICON_CACHE_DURATION;
    }

    /**
     * Carrega o cache do armazenamento local
     * @returns {Promise<void>}
     */
    async function loadFromStorage() {
        if (isLoading) return;
        isLoading = true;

        try {
            const result = await new Promise((resolve) => {
                chrome.storage.local.get([
                    CACHE_CONFIG.FAVICON_CACHE_KEY,
                    CACHE_CONFIG.CACHE_METADATA_KEY
                ], resolve);
            });

            // Carregar cache de favicons
            const cacheData = result[CACHE_CONFIG.FAVICON_CACHE_KEY] || {};
            memoryCache.clear();
            
            let validEntries = 0;
            for (const [key, entry] of Object.entries(cacheData)) {
                if (isCacheEntryValid(entry)) {
                    memoryCache.set(key, entry);
                    validEntries++;
                }
            }

            // Carregar metadados
            cacheMetadata = {
                ...cacheMetadata,
                ...(result[CACHE_CONFIG.CACHE_METADATA_KEY] || {})
            };

            console.log(`[FaviconCache] Cache carregado: ${validEntries} entradas válidas de ${Object.keys(cacheData).length} totais`);
            
        } catch (error) {
            console.error('[FaviconCache] Erro ao carregar cache:', error);
        } finally {
            isLoading = false;
        }
    }

    /**
     * Salva o cache no armazenamento local (com debounce)
     */
    function saveToStorage() {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(async () => {
            try {
                // Converter Map para objeto
                const cacheObj = {};
                for (const [key, value] of memoryCache.entries()) {
                    cacheObj[key] = value;
                }

                // Atualizar metadados
                cacheMetadata.lastSave = Date.now();

                await new Promise((resolve) => {
                    chrome.storage.local.set({
                        [CACHE_CONFIG.FAVICON_CACHE_KEY]: cacheObj,
                        [CACHE_CONFIG.CACHE_METADATA_KEY]: cacheMetadata
                    }, resolve);
                });

                console.log(`[FaviconCache] Cache salvo: ${memoryCache.size} entradas`);
                
            } catch (error) {
                console.error('[FaviconCache] Erro ao salvar cache:', error);
            }
        }, CACHE_CONFIG.DEBOUNCE_DELAY);
    }

    /**
     * Obtém uma entrada do cache
     * @param {string} url - URL do site
     * @returns {Object|null} Entrada do cache ou null
     */
    function get(url) {
        const key = generateCacheKey(url);
        if (!key) return null;

        cacheMetadata.totalRequests++;

        const entry = memoryCache.get(key);
        if (entry && isCacheEntryValid(entry)) {
            // Atualizar estatísticas de acesso
            entry.accessCount++;
            entry.lastAccess = Date.now();
            cacheMetadata.cacheHits++;
            
            return entry;
        }

        cacheMetadata.cacheMisses++;
        return null;
    }

    /**
     * Armazena uma entrada no cache
     * @param {string} url - URL do site
     * @param {string} faviconUrl - URL do favicon
     */
    function set(url, faviconUrl) {
        const key = generateCacheKey(url);
        if (!key) return;

        const entry = createCacheEntry(faviconUrl, url);
        memoryCache.set(key, entry);

        // Verificar limite de memória
        if (memoryCache.size > CACHE_CONFIG.MAX_MEMORY_CACHE_SIZE) {
            cleanupMemoryCache();
        }

        // Salvar no storage (com debounce)
        saveToStorage();
    }

    /**
     * Limpa o cache em memória mantendo apenas as entradas mais acessadas
     */
    function cleanupMemoryCache() {
        if (memoryCache.size <= CACHE_CONFIG.MAX_MEMORY_CACHE_SIZE) return;

        // Converter para array e ordenar por frequência de acesso e recência
        const entries = Array.from(memoryCache.entries())
            .map(([key, entry]) => ({
                key,
                entry,
                score: entry.accessCount * 0.7 + (Date.now() - entry.lastAccess) * -0.3
            }))
            .sort((a, b) => b.score - a.score);

        // Manter apenas as melhores entradas
        memoryCache.clear();
        const keepCount = Math.floor(CACHE_CONFIG.MAX_MEMORY_CACHE_SIZE * 0.8);
        
        for (let i = 0; i < keepCount && i < entries.length; i++) {
            memoryCache.set(entries[i].key, entries[i].entry);
        }

        console.log(`[FaviconCache] Cache em memória limpo: mantidas ${memoryCache.size} de ${entries.length} entradas`);
    }

    /**
     * Limpeza completa do cache (remove entradas expiradas)
     */
    async function cleanup() {
        const now = Date.now();
        
        // Evitar limpezas muito frequentes
        if (now - cacheMetadata.lastCleanup < CACHE_CONFIG.CLEANUP_INTERVAL) {
            return;
        }

        console.log('[FaviconCache] Iniciando limpeza do cache...');
        
        let removedCount = 0;
        const keysToRemove = [];

        // Identificar entradas expiradas
        for (const [key, entry] of memoryCache.entries()) {
            if (!isCacheEntryValid(entry)) {
                keysToRemove.push(key);
            }
        }

        // Remover entradas expiradas
        keysToRemove.forEach(key => {
            memoryCache.delete(key);
            removedCount++;
        });

        // Atualizar metadados
        cacheMetadata.lastCleanup = now;

        // Salvar mudanças
        if (removedCount > 0) {
            saveToStorage();
            console.log(`[FaviconCache] Limpeza concluída: ${removedCount} entradas removidas`);
        }
    }

    /**
     * Limpa todo o cache
     */
    async function clear() {
        memoryCache.clear();
        cacheMetadata = {
            lastCleanup: Date.now(),
            totalRequests: 0,
            cacheHits: 0,
            cacheMisses: 0,
            version: '2.0'
        };

        await new Promise((resolve) => {
            chrome.storage.local.remove([
                CACHE_CONFIG.FAVICON_CACHE_KEY,
                CACHE_CONFIG.CACHE_METADATA_KEY
            ], resolve);
        });

        console.log('[FaviconCache] Cache limpo completamente');
    }

    /**
     * Obtém estatísticas do cache
     * @returns {Object} Estatísticas
     */
    function getStats() {
        const hitRate = cacheMetadata.totalRequests > 0 
            ? (cacheMetadata.cacheHits / cacheMetadata.totalRequests * 100).toFixed(2)
            : 0;

        return {
            memorySize: memoryCache.size,
            totalRequests: cacheMetadata.totalRequests,
            cacheHits: cacheMetadata.cacheHits,
            cacheMisses: cacheMetadata.cacheMisses,
            hitRate: `${hitRate}%`,
            lastCleanup: new Date(cacheMetadata.lastCleanup).toLocaleString(),
            version: cacheMetadata.version
        };
    }

    /**
     * Inicializa o sistema de cache
     */
    async function init() {
        console.log('[FaviconCache] Inicializando sistema de cache...');
        
        await loadFromStorage();
        
        // Programar limpeza automática
        cleanupTimeout = setInterval(cleanup, CACHE_CONFIG.CLEANUP_INTERVAL);
        
        console.log('[FaviconCache] Sistema de cache inicializado');
    }

    // API pública
    window.FaviconCache = {
        init,
        get,
        set,
        clear,
        cleanup,
        getStats,
        
        // Para debug
        _getMemoryCache: () => memoryCache,
        _getMetadata: () => cacheMetadata
    };

})();
