/**
 * <PERSON>ste<PERSON> de Drag and Drop para favoritos e pastas
 * Permite arrastar elementos entre colunas e dentro da mesma coluna
 */

let draggedElement = null;
let draggedElementType = null; // 'bookmark' ou 'folder'
let originalParentId = null;
let originalIndex = null;
let dropTarget = null;
let dropPosition = null; // 'before', 'after', 'inside'
let dragStartX = 0;
let dragStartY = 0;
let isDragging = false;

// Variáveis para arraste múltiplo
let draggedElements = []; // Array de elementos sendo arrastados
let isMultipleDrag = false; // Flag para indicar arraste múltiplo

/**
 * Inicializa o sistema de drag and drop
 */
function initDragAndDrop() {
  console.log("Inicializando sistema de drag and drop...");

  // Verificar se os containers existem
  const bookmarksContainer = document.getElementById('bookmarksContainer');
  const folderContainer = document.getElementById('folderCheckboxes');

  console.log("Containers encontrados:", {
    bookmarks: !!bookmarksContainer,
    folders: !!folderContainer
  });

  setupBookmarkDragEvents();
  setupFolderDragEvents();
  setupDragStyles();

  // Adicionar um teste para verificar se elementos draggable são criados
  setTimeout(() => {
    const draggableBookmarks = document.querySelectorAll('.bookmark-item[draggable="true"]');
    const draggableFolders = document.querySelectorAll('.folder-option[draggable="true"]');

    console.log("Elementos draggable encontrados:", {
      bookmarks: draggableBookmarks.length,
      folders: draggableFolders.length
    });
  }, 2000);
}

/**
 * Configura estilos CSS necessários para o drag and drop
 */
function setupDragStyles() {
  const style = document.createElement('style');
  style.textContent = `
    /* Estilos para drag and drop */
    .dragging {
      opacity: 0.5;
      z-index: 1000;
    }

    .drop-before::before {
      content: '';
      position: absolute;
      top: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #0078d4;
      z-index: 10;
    }

    .drop-after::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #0078d4;
      z-index: 10;
    }

    .drop-inside {
      background-color: rgba(0, 120, 212, 0.1) !important;
      border: 2px dashed #0078d4 !important;
    }

    .drag-ghost {
      position: absolute;
      background: #0078d4;
      color: white;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 700;
      border: 3px solid #ffffff;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
      pointer-events: none;
      z-index: 1001;
      min-width: 80px;
      text-align: center;
      white-space: nowrap;
    }

    .bookmark-item, .folder-option {
      position: relative;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Configura eventos de drag para favoritos
 */
function setupBookmarkDragEvents() {
  const bookmarksContainer = document.getElementById('bookmarksContainer');

  if (!bookmarksContainer) {
    console.error("Container de favoritos não encontrado!");
    return;
  }

  console.log("Configurando eventos de drag para favoritos...");

  // Delegação de eventos para lidar com elementos dinâmicos
  bookmarksContainer.addEventListener('dragstart', handleBookmarkDragStart);
  bookmarksContainer.addEventListener('dragend', handleBookmarkDragEnd);
  bookmarksContainer.addEventListener('dragover', handleDragOver);
  bookmarksContainer.addEventListener('dragleave', handleDragLeave);
  bookmarksContainer.addEventListener('drop', handleDrop);
}

/**
 * Configura eventos de drag para pastas
 */
function setupFolderDragEvents() {
  const folderContainer = document.getElementById('folderCheckboxes');

  if (!folderContainer) {
    console.error("Container de pastas não encontrado!");
    return;
  }

  console.log("Configurando eventos de drag para pastas...");

  // Delegação de eventos para lidar com elementos dinâmicos
  folderContainer.addEventListener('dragstart', handleFolderDragStart);
  folderContainer.addEventListener('dragend', handleFolderDragEnd);
  folderContainer.addEventListener('dragover', handleDragOver);
  folderContainer.addEventListener('dragleave', handleDragLeave);
  folderContainer.addEventListener('drop', handleDrop);
}

/**
 * Manipula o início do arrasto de um favorito
 */
function handleBookmarkDragStart(e) {
  const target = e.target.closest('.bookmark-item');
  if (!target) return;

  // Verificar se o elemento é arrastável
  if (target.getAttribute('draggable') !== 'true') return;

  console.log("Iniciando drag de favorito:", target.dataset.id);

  // Verificar se há múltiplos favoritos selecionados
  const selectedBookmarks = getSelectedBookmarkElements();
  const targetIsSelected = selectedBookmarks.includes(target);

  if (selectedBookmarks.length > 1 && targetIsSelected) {
    // Arraste múltiplo - preparar todos os elementos selecionados
    console.log(`Iniciando arraste múltiplo de ${selectedBookmarks.length} favoritos`);

    isMultipleDrag = true;
    draggedElements = [...selectedBookmarks]; // Cópia do array
    draggedElement = target; // Elemento principal (onde o drag começou)

    // Adicionar classe de arrasto a todos os elementos selecionados
    draggedElements.forEach(element => {
      element.classList.add('dragging');
    });

    // Criar ghost personalizado para múltiplos itens
    const dragIcon = document.createElement('div');
    dragIcon.classList.add('drag-ghost');
    dragIcon.textContent = `${selectedBookmarks.length} favoritos`;
    document.body.appendChild(dragIcon);

    // Posicionar o ghost fora da tela inicialmente
    dragIcon.style.left = '-1000px';
    dragIcon.style.top = '-1000px';

    e.dataTransfer.setDragImage(dragIcon, -5, -5);

    // Remover o ghost após um pequeno delay
    setTimeout(() => {
      if (document.body.contains(dragIcon)) {
        document.body.removeChild(dragIcon);
      }
    }, 0);

  } else {
    // Arraste simples - comportamento original
    isMultipleDrag = false;
    draggedElements = [target];
    draggedElement = target;

    // Adicionar classe para estilo durante o arrasto
    target.classList.add('dragging');

    // Criar imagem de arrasto personalizada
    const bookmarkLink = target.querySelector('.bookmark-link');
    const bookmarkTitle = bookmarkLink ? bookmarkLink.textContent : 'Favorito';

    const dragIcon = document.createElement('div');
    dragIcon.classList.add('drag-ghost');
    dragIcon.textContent = bookmarkTitle;
    document.body.appendChild(dragIcon);

    // Posicionar o ghost fora da tela inicialmente
    dragIcon.style.left = '-1000px';
    dragIcon.style.top = '-1000px';

    e.dataTransfer.setDragImage(dragIcon, -5, -5);

    // Remover o ghost após um pequeno delay
    setTimeout(() => {
      if (document.body.contains(dragIcon)) {
        document.body.removeChild(dragIcon);
      }
    }, 0);
  }

  // Propriedades comuns
  draggedElementType = 'bookmark';
  originalParentId = target.dataset.folder;
  originalIndex = parseInt(target.dataset.index, 10);
  isDragging = true;

  dragStartX = e.clientX;
  dragStartY = e.clientY;

  // Definir dados para transferência
  e.dataTransfer.setData('text/plain', target.dataset.id);
  e.dataTransfer.effectAllowed = 'move';
}

/**
 * Manipula o fim do arrasto de um favorito
 */
function handleBookmarkDragEnd(e) {
  const target = e.target.closest('.bookmark-item');
  if (!target) return;

  console.log("Finalizando drag de favorito");

  // Remover classe de estilo de todos os elementos arrastados
  if (isMultipleDrag && draggedElements.length > 0) {
    draggedElements.forEach(element => {
      element.classList.remove('dragging');
    });
    console.log(`Finalizando arraste múltiplo de ${draggedElements.length} favoritos`);
  } else {
    target.classList.remove('dragging');
  }

  // Limpar variáveis
  draggedElement = null;
  draggedElements = [];
  isMultipleDrag = false;
  draggedElementType = null;
  originalParentId = null;
  originalIndex = null;
  isDragging = false;

  // Remover indicadores de drop
  removeDropIndicators();
}

/**
 * Manipula o início do arrasto de uma pasta
 */
function handleFolderDragStart(e) {
  const target = e.target.closest('.folder-option');
  if (!target) return;

  // Verificar se o elemento é arrastável
  if (target.getAttribute('draggable') !== 'true') return;

  console.log("Iniciando drag de pasta");

  // Obter o ID da pasta do checkbox
  const checkbox = target.querySelector('.folder-checkbox');
  if (!checkbox) return;

  const folderId = checkbox.value;

  draggedElement = target;
  draggedElementType = 'folder';
  originalParentId = target.dataset.parentId || '0'; // Root se não especificado
  originalIndex = parseInt(target.dataset.index, 10) || 0;
  isDragging = true;

  dragStartX = e.clientX;
  dragStartY = e.clientY;

  // Adicionar classe para estilo durante o arrasto
  target.classList.add('dragging');

  // Definir dados para transferência
  e.dataTransfer.setData('text/plain', folderId);
  e.dataTransfer.effectAllowed = 'move';

  // Criar imagem de arrasto personalizada
  const folderTitle = target.querySelector('.folder-title');
  const folderName = folderTitle ? folderTitle.textContent : 'Pasta';

  const dragIcon = document.createElement('div');
  dragIcon.classList.add('drag-ghost');
  dragIcon.textContent = folderName;
  document.body.appendChild(dragIcon);

  // Posicionar o ghost fora da tela inicialmente
  dragIcon.style.left = '-1000px';
  dragIcon.style.top = '-1000px';

  e.dataTransfer.setDragImage(dragIcon, -5, -5);

  // Remover o ghost após um pequeno delay
  setTimeout(() => {
    if (document.body.contains(dragIcon)) {
      document.body.removeChild(dragIcon);
    }
  }, 0);
}

/**
 * Manipula o fim do arrasto de uma pasta
 */
function handleFolderDragEnd(e) {
  const target = e.target.closest('.folder-option');
  if (!target) return;

  console.log("Finalizando drag de pasta");

  // Remover classe de estilo
  target.classList.remove('dragging');

  // Limpar variáveis
  draggedElement = null;
  draggedElementType = null;
  originalParentId = null;
  originalIndex = null;
  isDragging = false;

  // Remover indicadores de drop
  removeDropIndicators();
}

/**
 * Manipula o evento dragover para mostrar onde o elemento pode ser solto
 */
function handleDragOver(e) {
  if (!isDragging) return;

  e.preventDefault();
  e.dataTransfer.dropEffect = 'move';

  // Identificar o alvo potencial de drop
  const bookmarkTarget = e.target.closest('.bookmark-item');
  const folderTarget = e.target.closest('.folder-option');
  const bookmarksContainer = document.getElementById('bookmarksContainer');
  const foldersContainer = document.getElementById('folderCheckboxes');

  // Remover indicadores anteriores
  removeDropIndicators();

  // Não permitir drop no próprio elemento
  if ((bookmarkTarget && bookmarkTarget === draggedElement) ||
      (folderTarget && folderTarget === draggedElement)) {
    return;
  }

  if (bookmarkTarget && draggedElementType === 'bookmark') {
    // Favorito sobre favorito - determinar posição (antes ou depois)
    const rect = bookmarkTarget.getBoundingClientRect();
    const midY = rect.top + rect.height / 2;

    if (e.clientY < midY) {
      // Antes do favorito
      bookmarkTarget.classList.add('drop-before');
      dropPosition = 'before';
    } else {
      // Depois do favorito
      bookmarkTarget.classList.add('drop-after');
      dropPosition = 'after';
    }

    dropTarget = bookmarkTarget;
  } else if (folderTarget) {
    // Elemento sobre pasta - determinar posição (antes, depois ou dentro)
    const rect = folderTarget.getBoundingClientRect();
    const midY = rect.top + rect.height / 2;
    const threshold = rect.height * 0.3; // 30% da altura para determinar "dentro"

    if (e.clientY < midY - threshold) {
      // Antes da pasta
      folderTarget.classList.add('drop-before');
      dropPosition = 'before';
    } else if (e.clientY > midY + threshold) {
      // Depois da pasta
      folderTarget.classList.add('drop-after');
      dropPosition = 'after';
    } else {
      // Dentro da pasta (apenas para favoritos)
      if (draggedElementType === 'bookmark') {
        folderTarget.classList.add('drop-inside');
        dropPosition = 'inside';
      } else {
        // Para pastas, não permitir drop "dentro" por enquanto
        folderTarget.classList.add('drop-after');
        dropPosition = 'after';
      }
    }

    dropTarget = folderTarget;
  } else if (draggedElementType === 'bookmark' && bookmarksContainer && bookmarksContainer.contains(e.target)) {
    // Mouse sobre o container de favoritos mas não sobre um favorito específico
    // Verificar se deve inserir antes do primeiro elemento ou após o último
    const firstBookmark = bookmarksContainer.querySelector('.bookmark-item');

    if (firstBookmark) {
      const firstRect = firstBookmark.getBoundingClientRect();

      // Se o mouse está acima do primeiro favorito, mostrar indicador "before"
      if (e.clientY < firstRect.top) {
        firstBookmark.classList.add('drop-before');
        dropTarget = firstBookmark;
        dropPosition = 'before';
      } else {
        // Se está abaixo de todos os favoritos, inserir após o último
        const allBookmarks = Array.from(bookmarksContainer.querySelectorAll('.bookmark-item'));
        const lastBookmark = allBookmarks[allBookmarks.length - 1];

        if (lastBookmark) {
          const lastRect = lastBookmark.getBoundingClientRect();
          if (e.clientY > lastRect.bottom) {
            lastBookmark.classList.add('drop-after');
            dropTarget = lastBookmark;
            dropPosition = 'after';
          }
        }
      }
    }
  } else if (foldersContainer && foldersContainer.contains(e.target)) {
    // Mouse sobre o container de pastas mas não sobre uma pasta específica
    // Verificar se deve inserir antes da primeira pasta ou após a última
    const firstFolder = foldersContainer.querySelector('.folder-option');

    if (firstFolder) {
      const firstRect = firstFolder.getBoundingClientRect();

      // Se o mouse está acima da primeira pasta, mostrar indicador "before"
      if (e.clientY < firstRect.top) {
        firstFolder.classList.add('drop-before');
        dropTarget = firstFolder;
        dropPosition = 'before';
      } else {
        // Se está abaixo de todas as pastas, inserir após a última
        const allFolders = Array.from(foldersContainer.querySelectorAll('.folder-option'));
        const lastFolder = allFolders[allFolders.length - 1];

        if (lastFolder) {
          const lastRect = lastFolder.getBoundingClientRect();
          if (e.clientY > lastRect.bottom) {
            lastFolder.classList.add('drop-after');
            dropTarget = lastFolder;
            dropPosition = 'after';
          }
        }
      }
    }
  } else {
    // Limpar se não há alvo válido
    dropTarget = null;
    dropPosition = null;
  }
}

/**
 * Manipula o evento dragleave para remover indicadores visuais
 */
function handleDragLeave(e) {
  // Verificar se realmente saiu do elemento
  const relatedTarget = e.relatedTarget;
  if (relatedTarget && e.currentTarget.contains(relatedTarget)) {
    return; // Ainda dentro do container
  }
  
  removeDropIndicators();
}

/**
 * Remove todos os indicadores visuais de drop
 */
function removeDropIndicators() {
  document.querySelectorAll('.drop-before, .drop-after, .drop-inside').forEach(el => {
    el.classList.remove('drop-before', 'drop-after', 'drop-inside');
  });
}

/**
 * Manipula o evento drop para mover o elemento
 */
function handleDrop(e) {
  e.preventDefault();

  console.log("Drop detectado");

  // Remover indicadores visuais
  removeDropIndicators();

  // Se não há elemento arrastado ou alvo de drop, sair
  if (!draggedElement || !dropTarget || !dropPosition) {
    console.log("Drop cancelado: dados insuficientes");
    return;
  }

  // Obter IDs
  let draggedId;
  let targetId;

  if (draggedElementType === 'bookmark') {
    draggedId = draggedElement.dataset.id;
  } else if (draggedElementType === 'folder') {
    const checkbox = draggedElement.querySelector('.folder-checkbox');
    draggedId = checkbox ? checkbox.value : null;
  }

  if (dropTarget.classList.contains('bookmark-item')) {
    targetId = dropTarget.dataset.id;
  } else if (dropTarget.classList.contains('folder-option')) {
    const checkbox = dropTarget.querySelector('.folder-checkbox');
    targetId = checkbox ? checkbox.value : null;
  }

  if (!draggedId || !targetId) {
    console.log("Drop cancelado: IDs inválidos");
    return;
  }

  // Não permitir soltar em si mesmo
  if (draggedId === targetId && dropPosition !== 'inside') {
    console.log("Drop cancelado: mesmo elemento");
    return;
  }

  console.log(`Movendo ${draggedElementType} ${draggedId} para ${dropPosition} de ${targetId}`);

  // Determinar nova posição e parent ID
  let newParentId, newIndex;

  if (draggedElementType === 'bookmark') {
    if (dropTarget.classList.contains('bookmark-item')) {
      // Favorito para favorito - mover para a mesma pasta
      newParentId = dropTarget.dataset.folder;

      // Obter índice do alvo
      const targetIndex = parseInt(dropTarget.dataset.index, 10);

      // Ajustar índice com base na posição
      if (dropPosition === 'before') {
        newIndex = targetIndex;
      } else if (dropPosition === 'after') {
        newIndex = targetIndex + 1;
      }

      // Verificar se é arraste múltiplo
      if (isMultipleDrag && draggedElements.length > 1) {
        moveMultipleBookmarks(draggedElements, newParentId, newIndex);
      } else {
        // Mover o favorito na API do Chrome
        moveBookmark(draggedId, newParentId, newIndex);
      }
    } else if (dropTarget.classList.contains('folder-option') && dropPosition === 'inside') {
      // Favorito para dentro de pasta
      newParentId = targetId;

      // Verificar se é arraste múltiplo
      if (isMultipleDrag && draggedElements.length > 1) {
        moveMultipleBookmarks(draggedElements, newParentId);
      } else {
        // Mover para o final da pasta
        moveBookmark(draggedId, newParentId);
      }
    }
  } else if (draggedElementType === 'folder') {
    if (dropTarget.classList.contains('folder-option')) {
      // Pasta para pasta - por enquanto, apenas reordenar no mesmo nível
      // TODO: Implementar movimentação hierárquica de pastas
      console.log("Movimentação de pastas ainda não implementada completamente");
      showActionFeedback("Movimentação de pastas em desenvolvimento", "info");
    }
  }
}

/**
 * Move múltiplos favoritos mantendo a ordem relativa original
 */
function moveMultipleBookmarks(elements, newParentId, newIndex) {
  console.log(`Movendo ${elements.length} favoritos para pasta ${newParentId}, índice inicial ${newIndex}`);

  // Ordenar os elementos pela posição original no DOM para manter a ordem relativa
  const sortedElements = [...elements].sort((a, b) => {
    const aIndex = parseInt(a.dataset.index, 10) || 0;
    const bIndex = parseInt(b.dataset.index, 10) || 0;
    return aIndex - bIndex;
  });

  const sortedIds = sortedElements.map(element => element.dataset.id);

  console.log(`Ordem original dos favoritos:`, sortedIds);

  // Nova estratégia: mover todos para uma posição temporária primeiro,
  // depois mover para as posições finais corretas
  let movedCount = 0;
  let tempMoveCount = 0;

  // Primeiro, obter todos os favoritos da pasta de destino para calcular posições
  chrome.bookmarks.getChildren(newParentId, (children) => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao obter filhos da pasta:", chrome.runtime.lastError);
      showActionFeedback("Erro ao mover favoritos", "error");
      return;
    }

    const bookmarksInDestination = children.filter(child => child.url);
    console.log(`Pasta de destino tem ${bookmarksInDestination.length} favoritos`);

    // Fase 1: Mover todos os favoritos para o final da pasta (posição temporária)
    const tempIndex = bookmarksInDestination.length;

    const moveToTemp = (index) => {
      if (index >= sortedIds.length) {
        // Todos movidos para posição temporária, agora mover para posições finais
        console.log("Fase 1 concluída: todos favoritos movidos para posição temporária");
        moveToFinalPositions();
        return;
      }

      const bookmarkId = sortedIds[index];
      console.log(`Fase 1: Movendo favorito ${index + 1}/${sortedIds.length} para posição temporária`);

      chrome.bookmarks.move(bookmarkId, {
        parentId: newParentId,
        index: tempIndex + index
      }, () => {
        if (chrome.runtime.lastError) {
          console.error(`Erro ao mover favorito ${bookmarkId} para temp:`, chrome.runtime.lastError);
        } else {
          tempMoveCount++;
        }
        moveToTemp(index + 1);
      });
    };

    // Fase 2: Mover da posição temporária para as posições finais corretas
    const moveToFinalPositions = () => {
      console.log("Fase 2: Movendo para posições finais");

      // Obter o estado atual da pasta após a Fase 1 para calcular posições corretas
      chrome.bookmarks.getChildren(newParentId, (updatedChildren) => {
        if (chrome.runtime.lastError) {
          console.error("Erro ao obter estado atualizado da pasta:", chrome.runtime.lastError);
          showActionFeedback("Erro ao mover favoritos", "error");
          return;
        }

        const updatedBookmarks = updatedChildren.filter(child => child.url);
        console.log(`Estado após Fase 1: ${updatedBookmarks.length} favoritos na pasta`);

        // Os favoritos movidos estão no final da lista
        // Calcular quantos favoritos selecionados estavam ANTES da posição de destino original
        let favoritesRemovedBeforeTarget = 0;
        sortedElements.forEach(element => {
          const originalIndex = parseInt(element.dataset.index, 10) || 0;
          if (originalIndex < newIndex) {
            favoritesRemovedBeforeTarget++;
          }
        });

        // Ajustar o índice subtraindo os favoritos que foram removidos antes da posição de destino
        const adjustedNewIndex = Math.max(0, newIndex - favoritesRemovedBeforeTarget);

        console.log(`Favoritos removidos antes do destino: ${favoritesRemovedBeforeTarget}`);
        console.log(`Índice original: ${newIndex}, Índice ajustado: ${adjustedNewIndex}`);

        const moveFinal = (index) => {
          if (index >= sortedIds.length) {
            // Todos movidos para posições finais
            console.log(`Todos os ${movedCount} favoritos foram movidos com sucesso`);

            // Atualizar UI
            updateUIAfterMove();

            // Mostrar feedback
            showActionFeedback(`${movedCount} favoritos movidos com sucesso`, "success");
            return;
          }

          const bookmarkId = sortedIds[index];
          const finalIndex = adjustedNewIndex + index;

          console.log(`Fase 2: Movendo favorito ${index + 1}/${sortedIds.length} para posição final ${finalIndex}`);

          chrome.bookmarks.move(bookmarkId, {
            parentId: newParentId,
            index: finalIndex
          }, () => {
            if (chrome.runtime.lastError) {
              console.error(`Erro ao mover favorito ${bookmarkId} para posição final:`, chrome.runtime.lastError);
            } else {
              movedCount++;
              console.log(`Favorito ${bookmarkId} movido para posição final ${finalIndex}`);
            }
            moveFinal(index + 1);
          });
        };

        moveFinal(0);
      });
    };

    // Iniciar Fase 1
    moveToTemp(0);
  });
}

/**
 * Move um favorito para nova posição usando a API do Chrome
 */
function moveBookmark(bookmarkId, newParentId, newIndex) {
  console.log(`Movendo favorito ${bookmarkId} para pasta ${newParentId}, índice ${newIndex}`);

  const moveProperties = { parentId: newParentId };

  // Adicionar índice apenas se especificado
  if (newIndex !== undefined) {
    moveProperties.index = newIndex;
  }

  chrome.bookmarks.move(bookmarkId, moveProperties, () => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao mover favorito:", chrome.runtime.lastError);
      showActionFeedback("Erro ao mover favorito", "error");
      return;
    }

    console.log("Favorito movido com sucesso");

    // Atualizar UI
    updateUIAfterMove();

    showActionFeedback("Favorito movido com sucesso", "success");
  });
}

/**
 * Move uma pasta para nova posição usando a API do Chrome
 */
function moveFolder(folderId, newParentId, newIndex) {
  // Verificar se não está tentando mover para dentro de si mesmo
  if (folderId === newParentId) {
    showActionFeedback("Não é possível mover uma pasta para dentro dela mesma", "error");
    return;
  }
  
  // Verificar se não está tentando mover para dentro de um descendente
  chrome.bookmarks.getSubTree(folderId, (results) => {
    if (chrome.runtime.lastError) {
      console.error("Erro ao verificar árvore:", chrome.runtime.lastError);
      return;
    }
    
    // Função recursiva para verificar se o destino é descendente
    function isDescendant(node, targetId) {
      if (node.id === targetId) return true;
      if (node.children) {
        for (const child of node.children) {
          if (isDescendant(child, targetId)) return true;
        }
      }
      return false;
    }
    
    // Se o destino for descendente, abortar
    if (results[0] && isDescendant(results[0], newParentId)) {
      showActionFeedback("Não é possível mover uma pasta para dentro de sua subpasta", "error");
      return;
    }
    
    // Prosseguir com a movimentação
    const moveProperties = { parentId: newParentId };
    
    // Adicionar índice apenas se especificado
    if (newIndex !== undefined) {
      moveProperties.index = newIndex;
    }
    
    chrome.bookmarks.move(folderId, moveProperties, () => {
      if (chrome.runtime.lastError) {
        console.error("Erro ao mover pasta:", chrome.runtime.lastError);
        showActionFeedback("Erro ao mover pasta", "error");
        return;
      }

      console.log("Pasta movida com sucesso");

      // Recarregar a árvore de pastas
      if (typeof loadBookmarkFolders === 'function') {
        loadBookmarkFolders();
      } else {
        // Fallback: recarregar a página inteira
        chrome.bookmarks.getTree((tree) => {
          const roots = tree[0].children;
          populateFolderCheckboxes(roots, folderCheckboxesContainer);
          updateFolderCount();
        });
      }

      // Atualizar UI se a pasta movida ou destino estiver selecionada
      if (selectedFolderIds.has(folderId) || selectedFolderIds.has(newParentId)) {
        if (typeof reloadSelectedFolders === 'function') {
          reloadSelectedFolders();
        } else if (typeof window.reloadSelectedFolders === 'function') {
          window.reloadSelectedFolders();
        }
      }

      showActionFeedback("Pasta movida com sucesso", "success");
    });
  });
}

/**
 * Atualiza a UI após mover favoritos preservando a posição de scroll
 */
function updateUIAfterMove() {
  // Salvar a posição de scroll atual
  const bookmarksContainer = document.getElementById('bookmarksContainer');
  const savedScrollTop = bookmarksContainer ? bookmarksContainer.scrollTop : 0;

  console.log(`Salvando posição de scroll: ${savedScrollTop}px`);

  // Função para restaurar o scroll após a atualização
  const restoreScroll = () => {
    if (bookmarksContainer && savedScrollTop > 0) {
      // Usar setTimeout para garantir que o DOM foi atualizado
      setTimeout(() => {
        bookmarksContainer.scrollTop = savedScrollTop;
        console.log(`Scroll restaurado para: ${savedScrollTop}px`);
      }, 50);
    }
  };

  // Atualizar UI - recarregar pastas afetadas com preservação de scroll
  if (typeof reloadSelectedFolders === 'function') {
    reloadSelectedFolders(true); // Passar true para preservar scroll
  } else if (typeof window.reloadSelectedFolders === 'function') {
    window.reloadSelectedFolders(true); // Passar true para preservar scroll
  } else {
    // Fallback: recarregar manualmente as pastas selecionadas
    if (typeof selectedFolderIds !== 'undefined') {
      let foldersProcessed = 0;
      const totalFolders = selectedFolderIds.size;

      if (totalFolders === 0) {
        restoreScroll();
        return;
      }

      selectedFolderIds.forEach(folderId => {
        chrome.bookmarks.getChildren(folderId, (children) => {
          if (!chrome.runtime.lastError) {
            const bookmarks = children.filter(child => child.url);
            if (bookmarksContainer && typeof renderBookmarks === 'function') {
              renderBookmarks(bookmarks, bookmarksContainer, false, folderId, false);
            }
          }

          foldersProcessed++;
          // Restaurar scroll apenas após processar todas as pastas
          if (foldersProcessed === totalFolders) {
            restoreScroll();
          }
        });
      });
    } else {
      restoreScroll();
    }
  }
}