/**
 * Camada de Compatibilidade para Sistema de Pastas
 * 
 * Fornece funções de compatibilidade para que o código existente
 * continue funcionando com o novo sistema de renderização dinâmica.
 */

(function() {
    'use strict';

    /**
     * Função de compatibilidade para populateFolderCheckboxes
     * Redireciona para o novo sistema de renderização dinâmica
     * @param {Array} folders - Array de pastas (ignorado no novo sistema)
     * @param {HTMLElement} containerEl - Container das pastas
     */
    window.populateFolderCheckboxes = function(folders, containerEl) {
        console.log('[FolderCompatibility] Redirecionando populateFolderCheckboxes para DynamicFolderRenderer');
        
        if (window.DynamicFolderRenderer) {
            // Se o sistema dinâmico não foi inicializado, inicializar agora
            window.DynamicFolderRenderer.init(containerEl);
        } else {
            console.warn('[FolderCompatibility] DynamicFolderRenderer não disponível, usando fallback');
            populateFolderCheckboxesFallback(folders, containerEl);
        }
    };

    /**
     * Função de compatibilidade para loadBookmarkFolders
     * Força recarregamento do sistema dinâmico
     */
    window.loadBookmarkFolders = function() {
        console.log('[FolderCompatibility] Redirecionando loadBookmarkFolders para DynamicFolderRenderer');
        
        if (window.DynamicFolderRenderer) {
            window.DynamicFolderRenderer.forceReload();
        } else {
            console.warn('[FolderCompatibility] DynamicFolderRenderer não disponível');
            // Fallback: recarregar usando método antigo
            const container = document.getElementById("folderCheckboxes");
            if (container) {
                chrome.bookmarks.getTree((tree) => {
                    const roots = tree[0].children;
                    populateFolderCheckboxesFallback(roots, container);
                    if (typeof updateFolderCount === 'function') {
                        updateFolderCount();
                    }
                });
            }
        }
    };

    /**
     * Função de compatibilidade para updateFolderCount
     * Atualiza contador de pastas
     */
    window.updateFolderCount = function() {
        if (window.DynamicFolderRenderer) {
            // O sistema dinâmico já atualiza automaticamente
            return;
        }
        
        // Fallback: contar elementos na interface
        const container = document.getElementById("folderCheckboxes");
        const countElement = document.getElementById('folderCount');
        
        if (container && countElement) {
            const folderCount = container.querySelectorAll('.folder-option').length;
            countElement.textContent = `Pastas exibidas: ${folderCount}`;
        }
    };

    /**
     * Função fallback para renderização de pastas (sistema antigo)
     * @param {Array} folders - Array de pastas
     * @param {HTMLElement} containerEl - Container das pastas
     */
    function populateFolderCheckboxesFallback(folders, containerEl) {
        containerEl.innerHTML = ''; // Limpa antes

        folders.forEach((folder) => {
            if (!folder.url) { // Verifica se é uma pasta
                const label = document.createElement("label");
                label.className = "folder-option";

                // Habilitar drag and drop para pastas
                label.setAttribute("draggable", "true");

                const checkbox = document.createElement("input");
                checkbox.type = "checkbox";
                checkbox.value = folder.id;
                checkbox.className = "folder-checkbox";

                // Impedir que os checkboxes respondam diretamente aos cliques
                checkbox.style.pointerEvents = "none";

                checkbox.addEventListener("change", () => {
                    const folderId = checkbox.value;

                    // Aplicar alterações visuais imediatamente
                    if (checkbox.checked) {
                        if (typeof selectedFolderIds !== 'undefined') {
                            selectedFolderIds.add(folderId);
                        }
                        label.classList.add('selected');

                        // Atualizar contador
                        const selectedCountEl = document.getElementById("selectedFoldersCount");
                        if (selectedCountEl && typeof selectedFolderIds !== 'undefined') {
                            selectedCountEl.textContent = `Selecionadas: ${selectedFolderIds.size}`;
                            selectedCountEl.classList.add("has-selected");
                        }

                        // Carregar favoritos
                        if (typeof blockRendering === 'undefined' || !blockRendering) {
                            setTimeout(() => {
                                chrome.bookmarks.getChildren(folderId, (children) => {
                                    if (chrome.runtime.lastError) {
                                        console.error("Erro ao obter filhos:", chrome.runtime.lastError);
                                        return;
                                    }

                                    const items = children
                                        .filter(c => c.url)
                                        .sort((a, b) => (a.index || 0) - (b.index || 0));

                                    if (typeof renderBookmarks === 'function') {
                                        const bookmarksContainer = document.getElementById('bookmarksContainer');
                                        if (bookmarksContainer) {
                                            renderBookmarks(items, bookmarksContainer, false, folderId, false);
                                        }
                                    }
                                });
                            }, 0);
                        }
                    } else {
                        if (typeof selectedFolderIds !== 'undefined') {
                            selectedFolderIds.delete(folderId);
                        }
                        label.classList.remove('selected');

                        // Atualizar contador
                        const selectedCountEl = document.getElementById("selectedFoldersCount");
                        if (selectedCountEl && typeof selectedFolderIds !== 'undefined') {
                            selectedCountEl.textContent = `Selecionadas: ${selectedFolderIds.size}`;
                            if (selectedFolderIds.size === 0) {
                                selectedCountEl.classList.remove("has-selected");
                            }
                        }

                        // Remover favoritos
                        const bookmarksContainer = document.getElementById('bookmarksContainer');
                        if (bookmarksContainer) {
                            const elementsToRemove = bookmarksContainer.querySelectorAll(`[data-folder="${folderId}"]`);
                            elementsToRemove.forEach(el => el.remove());
                        }

                        if (typeof folderBookmarkMap !== 'undefined') {
                            folderBookmarkMap.delete(folderId);
                        }

                        if (typeof updateBookmarkCount === 'function') {
                            updateBookmarkCount();
                        }
                    }
                });

                // Evento de clique no label
                label.addEventListener("click", (e) => {
                    e.preventDefault();
                    checkbox.checked = !checkbox.checked;
                    checkbox.dispatchEvent(new Event('change'));
                });

                // Container do título
                const titleContainer = document.createElement("div");
                titleContainer.className = "folder-title-container";

                const titleSpan = document.createElement("span");
                titleSpan.className = "folder-title";
                titleSpan.textContent = folder.title || "(Sem nome)";

                titleContainer.appendChild(titleSpan);

                label.appendChild(checkbox);
                label.appendChild(titleContainer);
                containerEl.appendChild(label);

                // Recursivamente adiciona subpastas
                if (folder.children) {
                    const subContainer = document.createElement("div");
                    subContainer.className = "folder-nest";
                    containerEl.appendChild(subContainer);
                    populateFolderCheckboxesFallback(folder.children, subContainer);
                }
            }
        });
    }

    /**
     * Função de compatibilidade para getSelectedFolderElements
     * @returns {Array} Array de elementos de pastas selecionadas
     */
    window.getSelectedFolderElements = function() {
        const container = document.getElementById("folderCheckboxes");
        if (!container) return [];

        return Array.from(container.querySelectorAll('.folder-option.selected'));
    };

    /**
     * Função de compatibilidade para obter IDs das pastas selecionadas
     * @returns {Array} Array de IDs das pastas selecionadas
     */
    window.getSelectedFolderIds = function() {
        if (window.DynamicFolderRenderer) {
            return Array.from(window.DynamicFolderRenderer.getSelectedFolders());
        }
        
        // Fallback
        if (typeof selectedFolderIds !== 'undefined') {
            return Array.from(selectedFolderIds);
        }
        
        return [];
    };

    /**
     * Função de compatibilidade para definir pastas selecionadas
     * @param {Array|Set} folders - Pastas para selecionar
     */
    window.setSelectedFolders = function(folders) {
        if (window.DynamicFolderRenderer) {
            window.DynamicFolderRenderer.setSelectedFolders(folders);
        } else {
            // Fallback
            if (typeof selectedFolderIds !== 'undefined') {
                selectedFolderIds.clear();
                const folderArray = Array.isArray(folders) ? folders : Array.from(folders);
                folderArray.forEach(id => selectedFolderIds.add(id));
            }
        }
    };

    console.log('[FolderCompatibility] Camada de compatibilidade carregada');

})();
