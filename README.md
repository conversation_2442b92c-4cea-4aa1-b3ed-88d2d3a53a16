# Bookmark Folder Merger Extension

## About
A browser extension that helps you organize and merge your bookmarks efficiently. It provides an intuitive interface to manage, combine, and organize bookmark folders while maintaining folder structures and preventing duplicates.

## Features
- Manage your bookmarks
- Select and merge multiple bookmark folders
- Search through bookmarks and folders
- Light/Dark theme support
- Responsive and intuitive interface
- Keyboard shortcuts support
- Customizable visual settings

## Installation
1. Download the extension from your browser's store:
   1. Chrome Web Store (coming soon)
   2. Firefox Add-ons (coming soon)
   3. Microsoft Edge Add-ons (coming soon)
2. Or install manually:
   1. Clone this repository
   2. Open your browser's extension page
   3. Enable developer mode
   4. Load unpacked extension from the `Extensão` folder

## Usage
1. Click on the extension icon in your browser toolbar
2. Select the source folders you want to merge
3. Choose your merge options:
   1. Select target folder
   2. Choose sorting preferences
   3. Set duplicate handling
4. Click "Merge" to combine the selected folders

## Technical Details
- Built with vanilla JavaScript
- CSS custom properties for theming
- Responsive design with CSS Grid and Flexbox
- Browser storage API for settings persistence

## Contributing
Contributions are welcome! Please feel free to submit a Pull Request.

## License
This project is licensed under the MIT License - see the LICENSE file for details.

## Support
If you have any questions or run into issues, please open an issue in this repository.

## Acknowledgments
- Icons provided by Remix Icons
- Interface design inspired by modern browser UI patterns

---

Made with ❤️ by [Your Name]
