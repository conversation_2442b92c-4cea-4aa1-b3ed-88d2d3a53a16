/**
 * Sistema de Renderização Dinâmica de Bookmarks
 * 
 * Renderiza a segunda coluna de forma reativa, refletindo mudanças
 * em tempo real tanto por ações de background quanto por uso da extensão.
 */

window.DynamicBookmarkRenderer = window.DynamicBookmarkRenderer || {};

(function() {
    'use strict';

    // Estado do sistema
    let isInitialized = false;
    let currentBookmarks = new Map(); // bookmarkId -> bookmarkData
    let bookmarkElements = new Map(); // bookmarkId -> domElement
    let selectedBookmarks = new Set();
    let containerElement = null;
    let selectedFolders = new Set();

    // Configurações
    const CONFIG = {
        ENABLE_LOGS: true,
        ANIMATION_DURATION: 200,
        DEBOUNCE_DELAY: 100,
        BATCH_SIZE: 20
    };

    /**
     * Inicializa o sistema de renderização dinâmica
     * @param {HTMLElement} container - Container dos bookmarks
     */
    function init(container) {
        if (isInitialized) return;

        containerElement = container;
        console.log('[DynamicBookmarkRenderer] Inicializando sistema...');

        // Configurar listeners para mudanças
        setupBookmarkListeners();

        // Configurar listeners para mudanças de seleção de pastas
        setupFolderSelectionListeners();

        isInitialized = true;
        console.log('[DynamicBookmarkRenderer] Sistema inicializado');
    }

    /**
     * Configura listeners para mudanças nos bookmarks
     */
    function setupBookmarkListeners() {
        // Listener para remoção
        chrome.bookmarks.onRemoved.addListener((id, removeInfo) => {
            if (removeInfo.node && removeInfo.node.url) {
                handleBookmarkRemoved(id, removeInfo);
            }
        });

        // Listener para criação
        chrome.bookmarks.onCreated.addListener((id, bookmark) => {
            if (bookmark.url) {
                handleBookmarkCreated(id, bookmark);
            }
        });

        // Listener para alteração
        chrome.bookmarks.onChanged.addListener((id, changeInfo) => {
            handleBookmarkChanged(id, changeInfo);
        });

        // Listener para movimentação
        chrome.bookmarks.onMoved.addListener((id, moveInfo) => {
            handleBookmarkMoved(id, moveInfo);
        });

        console.log('[DynamicBookmarkRenderer] Listeners de bookmarks configurados');
    }

    /**
     * Configura listeners para mudanças de seleção de pastas
     */
    function setupFolderSelectionListeners() {
        // Observar mudanças no sistema de pastas
        if (window.DynamicFolderRenderer) {
            // Integração com sistema dinâmico de pastas
            console.log('[DynamicBookmarkRenderer] Integração com DynamicFolderRenderer configurada');
        }
    }

    /**
     * Carrega bookmarks das pastas selecionadas
     * @param {Set|Array} folderIds - IDs das pastas selecionadas
     */
    function loadBookmarksFromFolders(folderIds) {
        const folders = Array.isArray(folderIds) ? folderIds : Array.from(folderIds);
        selectedFolders = new Set(folders);

        if (folders.length === 0) {
            clearAllBookmarks();
            return;
        }

        console.log(`[DynamicBookmarkRenderer] Carregando bookmarks de ${folders.length} pastas`);

        // Limpar bookmarks atuais
        currentBookmarks.clear();
        
        // Carregar bookmarks de cada pasta
        const loadPromises = folders.map(folderId => loadBookmarksFromFolder(folderId));
        
        Promise.all(loadPromises).then(() => {
            renderAllBookmarks();
        }).catch(error => {
            console.error('[DynamicBookmarkRenderer] Erro ao carregar bookmarks:', error);
        });
    }

    /**
     * Carrega bookmarks de uma pasta específica
     * @param {string} folderId - ID da pasta
     * @returns {Promise} Promise que resolve quando bookmarks são carregados
     */
    function loadBookmarksFromFolder(folderId) {
        return new Promise((resolve, reject) => {
            chrome.bookmarks.getChildren(folderId, (children) => {
                if (chrome.runtime.lastError) {
                    console.error(`[DynamicBookmarkRenderer] Erro ao carregar pasta ${folderId}:`, chrome.runtime.lastError);
                    reject(chrome.runtime.lastError);
                    return;
                }

                // Filtrar apenas bookmarks (com URL) e ordenar
                const bookmarks = children
                    .filter(child => child.url)
                    .sort((a, b) => (a.index || 0) - (b.index || 0));

                // Adicionar ao mapa de bookmarks
                bookmarks.forEach(bookmark => {
                    currentBookmarks.set(bookmark.id, {
                        ...bookmark,
                        folderId: folderId
                    });
                });

                resolve(bookmarks);
            });
        });
    }

    /**
     * Renderiza todos os bookmarks na interface
     */
    function renderAllBookmarks() {
        if (!containerElement) return;

        console.log(`[DynamicBookmarkRenderer] Renderizando ${currentBookmarks.size} bookmarks`);

        // Limpar container
        containerElement.innerHTML = '';
        bookmarkElements.clear();

        // Agrupar bookmarks por pasta para manter ordem
        const bookmarksByFolder = new Map();
        currentBookmarks.forEach((bookmark, id) => {
            const folderId = bookmark.folderId;
            if (!bookmarksByFolder.has(folderId)) {
                bookmarksByFolder.set(folderId, []);
            }
            bookmarksByFolder.get(folderId).push(bookmark);
        });

        // Renderizar em lotes para melhor performance
        const allBookmarks = [];
        selectedFolders.forEach(folderId => {
            const folderBookmarks = bookmarksByFolder.get(folderId) || [];
            allBookmarks.push(...folderBookmarks);
        });

        renderBookmarksBatch(allBookmarks, 0);
    }

    /**
     * Renderiza bookmarks em lotes
     * @param {Array} bookmarks - Array de bookmarks
     * @param {number} startIndex - Índice inicial
     */
    function renderBookmarksBatch(bookmarks, startIndex) {
        const endIndex = Math.min(startIndex + CONFIG.BATCH_SIZE, bookmarks.length);
        const fragment = document.createDocumentFragment();

        for (let i = startIndex; i < endIndex; i++) {
            const bookmark = bookmarks[i];
            const element = createBookmarkElement(bookmark);
            if (element) {
                fragment.appendChild(element);
            }
        }

        containerElement.appendChild(fragment);

        // Continuar com próximo lote se necessário
        if (endIndex < bookmarks.length) {
            requestAnimationFrame(() => {
                renderBookmarksBatch(bookmarks, endIndex);
            });
        } else {
            // Renderização completa
            updateBookmarkCount();
            console.log(`[DynamicBookmarkRenderer] Renderização completa: ${bookmarks.length} bookmarks`);
        }
    }

    /**
     * Cria elemento DOM para um bookmark
     * @param {Object} bookmark - Dados do bookmark
     * @returns {HTMLElement} Elemento do bookmark
     */
    function createBookmarkElement(bookmark) {
        // Verificar se já existe no cache
        if (bookmarkElements.has(bookmark.id)) {
            const cachedElement = bookmarkElements.get(bookmark.id);
            updateBookmarkElement(cachedElement, bookmark);
            return cachedElement;
        }

        // Criar novo elemento
        const item = document.createElement("div");
        item.className = "bookmark-item";
        item.dataset.id = bookmark.id;
        item.dataset.folder = bookmark.folderId;
        item.dataset.index = bookmark.index || 0;

        // Checkbox para seleção
        const checkbox = document.createElement("input");
        checkbox.type = "checkbox";
        checkbox.className = "bookmark-checkbox";
        checkbox.checked = selectedBookmarks.has(bookmark.id);

        // Container de texto (título e URL)
        const textContainer = document.createElement("div");
        textContainer.className = "text-container";

        // Container do título
        const titleContainer = document.createElement("div");
        titleContainer.className = "title-container";

        // Favicon
        const favicon = document.createElement("img");
        favicon.className = "favicon";
        favicon.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='%23999' d='M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z'/%3E%3C/svg%3E";
        favicon.alt = "Favicon";

        // Link do título
        const titleLink = document.createElement("a");
        titleLink.className = "bookmark-link";
        titleLink.href = bookmark.url;
        titleLink.textContent = bookmark.title || bookmark.url;
        titleLink.title = bookmark.title || bookmark.url;
        titleLink.target = "_blank";

        // Container da URL
        const urlContainer = document.createElement("div");
        urlContainer.className = "url-container";
        urlContainer.textContent = bookmark.url;
        urlContainer.title = bookmark.url;

        // Container de ações
        const actionsContainer = document.createElement("div");
        actionsContainer.className = "actions";

        // Botão de remoção
        const removeBtn = document.createElement("button");
        removeBtn.className = "remove-btn";
        removeBtn.textContent = "×";
        removeBtn.title = "Remover favorito";

        // Montar estrutura
        titleContainer.appendChild(favicon);
        titleContainer.appendChild(titleLink);
        textContainer.appendChild(titleContainer);
        textContainer.appendChild(urlContainer);
        actionsContainer.appendChild(removeBtn);

        item.appendChild(checkbox);
        item.appendChild(textContainer);
        item.appendChild(actionsContainer);

        // Configurar eventos
        setupBookmarkEvents(item, bookmark);

        // Carregar favicon
        loadFavicon(favicon, bookmark.url);

        // Armazenar no cache
        bookmarkElements.set(bookmark.id, item);

        return item;
    }

    /**
     * Atualiza elemento existente com novos dados
     * @param {HTMLElement} element - Elemento a ser atualizado
     * @param {Object} bookmark - Novos dados do bookmark
     */
    function updateBookmarkElement(element, bookmark) {
        // Atualizar datasets
        element.dataset.folder = bookmark.folderId;
        element.dataset.index = bookmark.index || 0;

        // Atualizar título
        const titleLink = element.querySelector('.bookmark-link');
        if (titleLink) {
            titleLink.textContent = bookmark.title || bookmark.url;
            titleLink.href = bookmark.url;
            titleLink.title = bookmark.title || bookmark.url;
        }

        // Atualizar URL
        const urlContainer = element.querySelector('.url-container');
        if (urlContainer) {
            urlContainer.textContent = bookmark.url;
            urlContainer.title = bookmark.url;
        }

        // Recarregar favicon se URL mudou
        const favicon = element.querySelector('.favicon');
        if (favicon) {
            loadFavicon(favicon, bookmark.url);
        }
    }

    /**
     * Configura eventos para um elemento de bookmark
     * @param {HTMLElement} element - Elemento do bookmark
     * @param {Object} bookmark - Dados do bookmark
     */
    function setupBookmarkEvents(element, bookmark) {
        const checkbox = element.querySelector('.bookmark-checkbox');
        const removeBtn = element.querySelector('.remove-btn');

        // Evento de seleção
        checkbox.addEventListener('change', () => {
            if (checkbox.checked) {
                selectedBookmarks.add(bookmark.id);
                element.classList.add('selected');
            } else {
                selectedBookmarks.delete(bookmark.id);
                element.classList.remove('selected');
            }
            updateSelectedBookmarksCount();
        });

        // Evento de remoção
        removeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            if (confirm('Deseja remover este favorito?')) {
                removeBookmark(bookmark.id);
            }
        });

        // Evento de clique no item
        element.addEventListener('click', (e) => {
            if (e.target === checkbox || e.target === removeBtn) return;
            
            // Toggle seleção
            checkbox.checked = !checkbox.checked;
            checkbox.dispatchEvent(new Event('change'));
        });
    }

    /**
     * Carrega favicon para um elemento
     * @param {HTMLElement} faviconElement - Elemento da favicon
     * @param {string} url - URL do bookmark
     */
    function loadFavicon(faviconElement, url) {
        if (window.FaviconSystem && window.FaviconSystem.loadFavicon) {
            window.FaviconSystem.loadFavicon(faviconElement, url);
        }
    }

    /**
     * Remove um bookmark
     * @param {string} bookmarkId - ID do bookmark
     */
    function removeBookmark(bookmarkId) {
        chrome.bookmarks.remove(bookmarkId, () => {
            if (chrome.runtime.lastError) {
                console.error('[DynamicBookmarkRenderer] Erro ao remover bookmark:', chrome.runtime.lastError);
                return;
            }
            
            // A remoção será tratada pelo listener onRemoved
            console.log(`[DynamicBookmarkRenderer] Bookmark ${bookmarkId} removido`);
        });
    }

    /**
     * Trata remoção de bookmark
     * @param {string} id - ID do bookmark removido
     * @param {Object} removeInfo - Informações da remoção
     */
    function handleBookmarkRemoved(id, removeInfo) {
        console.log(`[DynamicBookmarkRenderer] Bookmark removido: ${id}`);

        // Remover do estado
        currentBookmarks.delete(id);
        selectedBookmarks.delete(id);

        // Remover elemento da interface com animação
        const element = bookmarkElements.get(id);
        if (element) {
            element.style.transition = `opacity ${CONFIG.ANIMATION_DURATION}ms ease-out`;
            element.style.opacity = '0';
            
            setTimeout(() => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
                bookmarkElements.delete(id);
                updateBookmarkCount();
            }, CONFIG.ANIMATION_DURATION);
        }

        updateSelectedBookmarksCount();
    }

    /**
     * Trata criação de bookmark
     * @param {string} id - ID do bookmark criado
     * @param {Object} bookmark - Dados do bookmark
     */
    function handleBookmarkCreated(id, bookmark) {
        // Verificar se o bookmark está em uma pasta selecionada
        if (!selectedFolders.has(bookmark.parentId)) return;

        console.log(`[DynamicBookmarkRenderer] Bookmark criado: ${id}`);

        // Adicionar ao estado
        currentBookmarks.set(id, {
            ...bookmark,
            folderId: bookmark.parentId
        });

        // Criar e adicionar elemento
        const element = createBookmarkElement({
            ...bookmark,
            folderId: bookmark.parentId
        });

        if (element) {
            // Adicionar com animação
            element.style.opacity = '0';
            containerElement.appendChild(element);
            
            requestAnimationFrame(() => {
                element.style.transition = `opacity ${CONFIG.ANIMATION_DURATION}ms ease-in`;
                element.style.opacity = '1';
            });

            updateBookmarkCount();
        }
    }

    /**
     * Trata alteração de bookmark
     * @param {string} id - ID do bookmark alterado
     * @param {Object} changeInfo - Informações da alteração
     */
    function handleBookmarkChanged(id, changeInfo) {
        const bookmark = currentBookmarks.get(id);
        if (!bookmark) return;

        console.log(`[DynamicBookmarkRenderer] Bookmark alterado: ${id}`);

        // Atualizar dados
        if (changeInfo.title !== undefined) {
            bookmark.title = changeInfo.title;
        }
        if (changeInfo.url !== undefined) {
            bookmark.url = changeInfo.url;
        }

        // Atualizar elemento na interface
        const element = bookmarkElements.get(id);
        if (element) {
            updateBookmarkElement(element, bookmark);
        }
    }

    /**
     * Trata movimentação de bookmark
     * @param {string} id - ID do bookmark movido
     * @param {Object} moveInfo - Informações da movimentação
     */
    function handleBookmarkMoved(id, moveInfo) {
        console.log(`[DynamicBookmarkRenderer] Bookmark movido: ${id}`);

        const bookmark = currentBookmarks.get(id);
        if (!bookmark) return;

        // Verificar se ainda está em pasta selecionada
        if (selectedFolders.has(moveInfo.parentId)) {
            // Atualizar dados
            bookmark.parentId = moveInfo.parentId;
            bookmark.folderId = moveInfo.parentId;
            bookmark.index = moveInfo.index;

            // Atualizar elemento
            const element = bookmarkElements.get(id);
            if (element) {
                updateBookmarkElement(element, bookmark);
            }
        } else {
            // Removido de pasta selecionada
            handleBookmarkRemoved(id, { node: bookmark });
        }
    }

    /**
     * Limpa todos os bookmarks
     */
    function clearAllBookmarks() {
        if (containerElement) {
            containerElement.innerHTML = '';
        }
        currentBookmarks.clear();
        bookmarkElements.clear();
        selectedBookmarks.clear();
        updateBookmarkCount();
        updateSelectedBookmarksCount();
    }

    /**
     * Atualiza contador de bookmarks
     */
    function updateBookmarkCount() {
        const countElement = document.getElementById('bookmarksDisplayCount');
        if (countElement) {
            const count = currentBookmarks.size;
            countElement.textContent = `Favoritos exibidos: ${count}`;
            
            if (count > 0) {
                countElement.classList.add('has-bookmarks');
            } else {
                countElement.classList.remove('has-bookmarks');
            }
        }
    }

    /**
     * Atualiza contador de bookmarks selecionados
     */
    function updateSelectedBookmarksCount() {
        // Integrar com sistema existente se disponível
        if (typeof updateSelectedBookmarksCount === 'function') {
            updateSelectedBookmarksCount();
        }
    }

    /**
     * Obtém estatísticas do sistema
     */
    function getStats() {
        return {
            initialized: isInitialized,
            totalBookmarks: currentBookmarks.size,
            selectedBookmarks: selectedBookmarks.size,
            cachedElements: bookmarkElements.size,
            selectedFolders: selectedFolders.size
        };
    }

    /**
     * Força recarregamento completo
     */
    function forceReload() {
        console.log('[DynamicBookmarkRenderer] Forçando recarregamento...');
        
        if (selectedFolders.size > 0) {
            loadBookmarksFromFolders(selectedFolders);
        } else {
            clearAllBookmarks();
        }
    }

    /**
     * Obtém bookmarks selecionados (compatibilidade)
     * @returns {Set} Set de IDs dos bookmarks selecionados
     */
    function getSelectedBookmarks() {
        return new Set(selectedBookmarks);
    }

    /**
     * Define bookmarks selecionados (compatibilidade)
     * @param {Set|Array} bookmarks - Bookmarks para selecionar
     */
    function setSelectedBookmarks(bookmarks) {
        selectedBookmarks.clear();

        const bookmarkArray = Array.isArray(bookmarks) ? bookmarks : Array.from(bookmarks);
        bookmarkArray.forEach(bookmarkId => {
            selectedBookmarks.add(bookmarkId);

            // Atualizar elemento visual
            const element = bookmarkElements.get(bookmarkId);
            if (element) {
                const checkbox = element.querySelector('.bookmark-checkbox');
                if (checkbox) {
                    checkbox.checked = true;
                    element.classList.add('selected');
                }
            }
        });

        updateSelectedBookmarksCount();
    }

    // API pública
    window.DynamicBookmarkRenderer = {
        init,
        loadBookmarksFromFolders,
        clearAllBookmarks,
        forceReload,
        getStats,
        getSelectedBookmarks,
        setSelectedBookmarks
    };

    // Comandos globais para debug
    window.reloadBookmarks = () => forceReload();
    window.bookmarkRendererStats = () => getStats();

    console.log('[DynamicBookmarkRenderer] Sistema carregado');

})();
